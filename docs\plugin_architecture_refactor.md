# 插件架构重构指南

## 概述

本文档描述了插件系统的重构方案，解决了当前架构中"注册时过早执行内容生成"的问题，并提供了更清晰、更高效的插件开发体验。

## 当前问题分析

### 1. 问题识别

**问题1：注册时过早调用内容生成**
```go
// 当前问题：在插件注册时就会调用generateTitleAndContent()
func (b *BasePluginWrapper) GetName() string {
    config := b.basePlugin.GetPluginConfig(context.Background()) // 这里会执行内容生成
    return config.PluginID
}
```

**问题2：设计耦合度高**
- 插件的基本信息（ID、描述）与动态内容生成逻辑耦合
- 无法区分静态配置信息和动态执行内容

**问题3：执行时机不当**
- 签到、API调用等操作在插件注册时就执行
- 应该在调度时间到达时才执行

## 重构方案

### 1. 新架构设计

#### 核心思想
- **分离关注点**：将静态信息、调度配置、动态内容生成分离
- **延迟执行**：内容生成推迟到实际执行时
- **简化接口**：保持插件开发者的使用体验简单直观

#### 新接口定义
```go
// 新插件接口 - 分离静态信息和动态内容
type BasePlugin interface {
    // 获取插件静态信息（仅在注册时调用一次）
    GetStaticInfo() *PluginStaticInfo
    
    // 获取插件调度配置（仅在注册时调用一次）
    GetScheduleConfig() *PluginScheduleConfig
    
    // 生成运行时内容（每次执行时调用）
    GenerateContent(ctx context.Context) (*PluginRuntimeContent, error)
}
```

#### 数据结构
```go
// 插件静态信息
type PluginStaticInfo struct {
    PluginID          string // 插件唯一标识
    PluginDescription string // 插件描述
}

// 插件调度配置
type PluginScheduleConfig struct {
    Schedule       types.ScheduleConfig // 调度配置
    Enabled        bool                 // 是否启用
    RunImmediately bool                 // 是否立即执行
}

// 插件运行时内容
type PluginRuntimeContent struct {
    Title           string                // 消息标题
    DingTalkContent string                // 钉钉消息内容
    EmailContent    string                // 邮件消息内容
    MessageTypes    []types.MessageType   // 要发送的消息类型
    EmailConfig     *types.EmailConfig    // 邮件配置（可选）
    DingTalkConfig  *types.DingTalkConfig // 钉钉配置（可选）
}
```

### 2. 插件开发示例

#### 新版插件实现
```go
type SimpleReminderPluginV2 struct{}

// 静态信息（注册时调用一次）
func (p *SimpleReminderPluginV2) GetStaticInfo() *plugin.PluginStaticInfo {
    return &plugin.PluginStaticInfo{
        PluginID:          "simple_reminder_v2",
        PluginDescription: "简单定时提醒插件 - 重构版本",
    }
}

// 调度配置（注册时调用一次）
func (p *SimpleReminderPluginV2) GetScheduleConfig() *plugin.PluginScheduleConfig {
    return &plugin.PluginScheduleConfig{
        Schedule: types.ScheduleConfig{
            Type:     types.CronSchedule,
            CronExpr: "0 */2 * * * *", // 每2分钟执行一次
        },
        Enabled:        true,
        RunImmediately: false,
    }
}

// 内容生成（每次执行时调用）
func (p *SimpleReminderPluginV2) GenerateContent(ctx context.Context) (*plugin.PluginRuntimeContent, error) {
    // 实时生成消息内容
    currentTime := time.Now().Format("2006-01-02 15:04:05")
    title := "⏰ 定时提醒"
    
    dingTalkContent := fmt.Sprintf("提醒时间: %s", currentTime)
    emailContent := fmt.Sprintf("<p>提醒时间: %s</p>", currentTime)
    
    return &plugin.PluginRuntimeContent{
        Title:           title,
        DingTalkContent: dingTalkContent,
        EmailContent:    emailContent,
        MessageTypes:    []types.MessageType{types.DingTalkMessage, types.EmailMessage},
    }, nil
}
```

### 3. 向后兼容性

#### 旧版插件适配器
```go
// 旧版插件仍然可以使用
legacyPlugin := &OldPlugin{}
wrapper := plugin.NewLegacyBasePlugin(legacyPlugin) // 自动适配

// 新版插件使用新接口
newPlugin := &NewPlugin{}
wrapper := plugin.NewBasePlugin(newPlugin)

// 自动检测插件类型
wrapper := plugin.NewAutoPlugin(somePlugin) // 自动选择合适的包装器
```

### 4. 迁移步骤

#### 步骤1：分析现有插件
```go
// 旧版插件结构
func (p *OldPlugin) GetPluginConfig(ctx context.Context) *BasePluginConfig {
    // 问题：这里会执行内容生成逻辑
    title, content := generateContent() // ← 需要移动到新方法
    
    return &BasePluginConfig{
        PluginID: "old_plugin",           // ← 移动到GetStaticInfo
        PluginDescription: "旧插件",      // ← 移动到GetStaticInfo
        TaskConfig: &types.TaskConfig{
            Schedule: ...,                // ← 移动到GetScheduleConfig
            // 其他配置...
        },
    }
}
```

#### 步骤2：拆分方法
```go
// 新版插件结构
func (p *NewPlugin) GetStaticInfo() *PluginStaticInfo {
    return &PluginStaticInfo{
        PluginID:          "new_plugin",
        PluginDescription: "新插件",
    }
}

func (p *NewPlugin) GetScheduleConfig() *PluginScheduleConfig {
    return &PluginScheduleConfig{
        Schedule: types.ScheduleConfig{...},
        Enabled:  true,
    }
}

func (p *NewPlugin) GenerateContent(ctx context.Context) (*PluginRuntimeContent, error) {
    // 内容生成逻辑移到这里
    title, content := generateContent()
    
    return &PluginRuntimeContent{
        Title:           title,
        DingTalkContent: content,
        MessageTypes:    []types.MessageType{types.DingTalkMessage},
    }, nil
}
```

#### 步骤3：更新注册代码
```go
// 旧版注册方式（仍然支持）
manager.RegisterPlugins(
    plugin.NewLegacyBasePlugin(&OldPlugin{}),
)

// 新版注册方式
manager.RegisterPlugins(
    plugin.NewBasePlugin(&NewPlugin{}),
)

// 自动检测方式
manager.RegisterPlugins(
    plugin.NewAutoPlugin(&SomePlugin{}),
)
```

### 5. 优势对比

#### 性能优势
- **注册阶段**：新架构避免了不必要的内容生成，注册更快
- **执行阶段**：内容生成只在需要时进行，减少资源浪费
- **内存使用**：静态信息缓存，避免重复计算

#### 设计优势
- **关注点分离**：静态信息、调度配置、动态内容各司其职
- **可测试性**：每个方法职责单一，更容易测试
- **可维护性**：代码结构更清晰，易于理解和修改

#### 开发体验
- **简单直观**：接口设计符合直觉，易于理解
- **灵活性**：支持条件性消息发送，更灵活的控制逻辑
- **向后兼容**：现有插件无需立即迁移

### 6. 最佳实践

#### 条件性消息发送
```go
func (p *Plugin) GenerateContent(ctx context.Context) (*PluginRuntimeContent, error) {
    // 根据条件决定是否发送消息
    if !shouldSendMessage() {
        return &PluginRuntimeContent{
            MessageTypes: []types.MessageType{}, // 空列表表示不发送
        }, nil
    }
    
    // 生成并返回消息内容
    return &PluginRuntimeContent{...}, nil
}
```

#### 错误处理
```go
func (p *Plugin) GenerateContent(ctx context.Context) (*PluginRuntimeContent, error) {
    data, err := fetchExternalData()
    if err != nil {
        // 返回错误，系统会跳过此次执行
        return nil, fmt.Errorf("获取数据失败: %v", err)
    }
    
    return &PluginRuntimeContent{...}, nil
}
```

#### 配置覆盖
```go
func (p *Plugin) GenerateContent(ctx context.Context) (*PluginRuntimeContent, error) {
    return &PluginRuntimeContent{
        Title:           "自定义标题",
        DingTalkContent: "自定义内容",
        MessageTypes:    []types.MessageType{types.DingTalkMessage},
        // 使用自定义配置覆盖默认配置
        DingTalkConfig: &types.DingTalkConfig{
            WebhookURL: "custom_webhook_url",
            Title:      "自定义标题",
            Text:       "自定义内容",
        },
    }, nil
}
```

## 总结

新的插件架构通过分离静态信息和动态内容生成，解决了当前架构的主要问题：

1. **延迟执行**：内容生成推迟到实际需要时
2. **性能提升**：减少不必要的计算和资源消耗
3. **设计清晰**：职责分离，代码更易维护
4. **向后兼容**：现有插件可以平滑迁移
5. **开发友好**：简单直观的接口设计

建议逐步将现有插件迁移到新架构，以获得更好的性能和开发体验。
