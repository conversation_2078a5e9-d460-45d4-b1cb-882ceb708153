package scheduler

import (
	"fmt"

	"taskReminder/types"
)

// 添加cron任务
func (s *TaskScheduler) addCronTask(taskID string, config *types.TaskConfig) error {
	if config.Schedule.CronExpr == "" {
		return ErrCronExpressionEmpty
	}

	_, err := s.cron.AddFunc(config.Schedule.CronExpr, func() {
		s.executeTask(taskID, config)
	})

	if err != nil {
		return fmt.Errorf("添加cron任务失败: %v", err)
	}

	s.logger.Printf("添加cron任务成功: %s (%s)", taskID, config.Schedule.CronExpr)
	return nil
}
