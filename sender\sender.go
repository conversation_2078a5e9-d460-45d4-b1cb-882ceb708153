package sender

import (
	"context"
	"fmt"

	"taskReminder/types"
)

// 统一消息发送器
type UnifiedSender struct {
	emailSender    *EmailSender
	dingTalkSender *DingTalkSender
}

// 创建新的统一发送器
func NewUnifiedSender() *UnifiedSender {
	return &UnifiedSender{
		emailSender:    NewEmailSender(),
		dingTalkSender: NewDingTalkSender(),
	}
}

// 发送邮件
func (u *UnifiedSender) SendEmail(ctx context.Context, config *types.EmailConfig) error {
	return u.emailSender.SendEmail(ctx, config)
}

// 发送钉钉消息
func (u *UnifiedSender) SendDingTalk(ctx context.Context, config *types.DingTalkConfig) error {
	return u.dingTalkSender.SendDingTalk(ctx, config)
}

// 根据任务配置发送消息 - 支持并发发送
func (u *UnifiedSender) SendMessages(ctx context.Context, config *types.TaskConfig) error {
	if len(config.MessageTypes) == 0 {
		return nil
	}

	// 如果只有一种消息类型，直接发送
	if len(config.MessageTypes) == 1 {
		return u.sendSingleMessage(ctx, config.MessageTypes[0], config)
	}

	// 多种消息类型时，使用并发发送
	return u.sendMessagesAsync(ctx, config)
}

// 发送单个消息类型
func (u *UnifiedSender) sendSingleMessage(ctx context.Context, msgType types.MessageType, config *types.TaskConfig) error {
	switch msgType {
	case types.EmailMessage:
		if config.Email != nil {
			if err := u.SendEmail(ctx, config.Email); err != nil {
				return fmt.Errorf("邮件发送失败: %v", err)
			}
		} else {
			return fmt.Errorf("邮件配置为空")
		}
	case types.DingTalkMessage:
		if config.DingTalk != nil {
			if err := u.SendDingTalk(ctx, config.DingTalk); err != nil {
				return fmt.Errorf("钉钉消息发送失败: %v", err)
			}
		} else {
			return fmt.Errorf("钉钉配置为空")
		}
	default:
		return fmt.Errorf("不支持的消息类型: %d", msgType)
	}
	return nil
}

// 并发发送多种消息类型
func (u *UnifiedSender) sendMessagesAsync(ctx context.Context, config *types.TaskConfig) error {
	// 创建错误通道，用于收集各个goroutine的错误
	errChan := make(chan error, len(config.MessageTypes))

	// 为每种消息类型启动一个goroutine
	for _, msgType := range config.MessageTypes {
		go func(mt types.MessageType) {
			errChan <- u.sendSingleMessage(ctx, mt, config)
		}(msgType)
	}

	// 收集所有goroutine的结果
	var errors []error
	for i := 0; i < len(config.MessageTypes); i++ {
		if err := <-errChan; err != nil {
			errors = append(errors, err)
		}
	}

	// 如果有错误，返回合并的错误信息
	if len(errors) > 0 {
		var errMsg string
		for i, err := range errors {
			if i > 0 {
				errMsg += "; "
			}
			errMsg += err.Error()
		}
		return fmt.Errorf("消息发送出现错误: %s", errMsg)
	}

	return nil
}
