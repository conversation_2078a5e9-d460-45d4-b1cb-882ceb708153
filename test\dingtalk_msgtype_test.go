package test

import (
	"encoding/json"
	"log"
	"testing"

	"taskReminder/sender"
	"taskReminder/types"
)

// 测试钉钉消息类型功能
func TestDingTalkMessageTypes(t *testing.T) {
	log.Println("=== 测试钉钉消息类型功能 ===")

	// 测试用例
	testCases := []struct {
		name        string
		config      *types.DingTalkConfig
		description string
	}{
		{
			name: "默认Markdown消息",
			config: &types.DingTalkConfig{
				WebhookURL: "https://oapi.dingtalk.com/robot/send?access_token=test_token",
				Title:      "测试标题",
				Text:       "这是一条**测试消息**，支持*Markdown*格式。",
				// MsgType 未设置，应该默认为 markdown
			},
			description: "测试默认消息类型（应为markdown）",
		},
		{
			name: "显式Markdown消息",
			config: &types.DingTalkConfig{
				WebhookURL: "https://oapi.dingtalk.com/robot/send?access_token=test_token",
				Title:      "Markdown测试",
				Text:       "## 二级标题\n\n这是一条**粗体文本**和*斜体文本*的测试。\n\n- 列表项1\n- 列表项2",
				MsgType:    types.DingTalkMarkdown,
			},
			description: "测试显式设置的Markdown消息",
		},
		{
			name: "文本消息",
			config: &types.DingTalkConfig{
				WebhookURL: "https://oapi.dingtalk.com/robot/send?access_token=test_token",
				Title:      "文本测试",
				Text:       "这是一条纯文本消息，不支持Markdown格式。",
				MsgType:    types.DingTalkText,
			},
			description: "测试文本消息类型",
		},
		{
			name: "带@功能的Markdown消息",
			config: &types.DingTalkConfig{
				WebhookURL: "https://oapi.dingtalk.com/robot/send?access_token=test_token",
				Title:      "重要通知",
				Text:       "## 🚨 重要通知\n\n请所有人注意这条消息！",
				MsgType:    types.DingTalkMarkdown,
				AtAll:      true,
			},
			description: "测试带@所有人功能的Markdown消息",
		},
	}

	// 执行测试用例
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			log.Printf("--- %s ---", tc.description)
			log.Printf("配置: %+v", tc.config)

			// 这里我们不实际发送消息，而是验证消息结构的构建
			// 通过检查序列化后的JSON来验证消息格式是否正确
			err := validateDingTalkMessage(tc.config)
			if err != nil {
				t.Errorf("消息验证失败: %v", err)
				return
			}

			log.Printf("✅ %s 验证通过", tc.name)
		})
	}
}

// 验证钉钉消息结构
func validateDingTalkMessage(config *types.DingTalkConfig) error {
	// 模拟发送器的消息构建逻辑
	msgType := config.MsgType
	if msgType == "" {
		msgType = types.DingTalkMarkdown
	}

	// 创建消息结构
	message := sender.DingTalkMessage{
		MsgType: string(msgType),
	}
	message.At.AtMobiles = config.AtMobiles
	message.At.IsAtAll = config.AtAll

	// 根据消息类型构建内容
	switch msgType {
	case types.DingTalkText:
		content := config.Text
		if config.Title != "" {
			content = config.Title + "\n\n" + config.Text
		}
		message.Text = &sender.TextContent{
			Content: content,
		}
	case types.DingTalkMarkdown:
		title := config.Title
		if title == "" {
			title = "消息通知"
		}
		content := config.Text
		if config.Title != "" {
			content = "## " + config.Title + "\n\n" + config.Text
		}
		message.Markdown = &sender.MarkdownContent{
			Title: title,
			Text:  content,
		}
	}

	// 序列化消息以验证结构
	messageBytes, err := json.Marshal(message)
	if err != nil {
		return err
	}

	log.Printf("生成的消息JSON: %s", string(messageBytes))
	return nil
}

// 测试消息类型常量
func TestDingTalkMsgTypeConstants(t *testing.T) {
	log.Println("=== 测试钉钉消息类型常量 ===")

	// 验证常量值
	if types.DingTalkText != "text" {
		t.Errorf("DingTalkText 常量值错误，期望: text，实际: %s", types.DingTalkText)
	}

	if types.DingTalkMarkdown != "markdown" {
		t.Errorf("DingTalkMarkdown 常量值错误，期望: markdown，实际: %s", types.DingTalkMarkdown)
	}

	log.Println("✅ 消息类型常量验证通过")
}
