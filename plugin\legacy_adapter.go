package plugin

import (
	"context"
	"taskReminder/types"
)

// 旧版插件配置结构（向后兼容）
type BasePluginConfig struct {
	// 基本信息
	PluginID          string // 插件英文名称（唯一标识）
	PluginDescription string // 插件描述

	// 完整的任务配置 - 包含所有发送方式的配置
	TaskConfig *types.TaskConfig // 完整任务配置，包含所有信息
}

// 旧版插件接口（向后兼容）
type LegacyBasePlugin interface {
	// 获取插件配置（每次调用都会重新生成，支持动态内容）
	// 用户通过设置MessageTypes来控制是否发送消息
	GetPluginConfig(ctx context.Context) *BasePluginConfig
}

// 旧版插件适配器 - 将旧版插件适配到新架构
type LegacyPluginAdapter struct {
	legacyPlugin LegacyBasePlugin
	staticInfo   *PluginStaticInfo
	scheduleConfig *PluginScheduleConfig
}

// NewLegacyAdapter 创建旧版插件适配器
func NewLegacyAdapter(legacyPlugin LegacyBasePlugin) *LegacyPluginAdapter {
	adapter := &LegacyPluginAdapter{
		legacyPlugin: legacyPlugin,
	}
	
	// 获取一次配置来提取静态信息
	config := legacyPlugin.GetPluginConfig(context.Background())
	if config != nil {
		adapter.staticInfo = &PluginStaticInfo{
			PluginID:          config.PluginID,
			PluginDescription: config.PluginDescription,
		}
		
		if config.TaskConfig != nil {
			adapter.scheduleConfig = &PluginScheduleConfig{
				Schedule:       config.TaskConfig.Schedule,
				Enabled:        config.TaskConfig.Enabled,
				RunImmediately: config.TaskConfig.RunImmediately,
			}
		}
	}
	
	return adapter
}

// GetStaticInfo 实现新接口
func (a *LegacyPluginAdapter) GetStaticInfo() *PluginStaticInfo {
	if a.staticInfo != nil {
		return a.staticInfo
	}
	return &PluginStaticInfo{
		PluginID:          "legacy_plugin",
		PluginDescription: "旧版插件",
	}
}

// GetScheduleConfig 实现新接口
func (a *LegacyPluginAdapter) GetScheduleConfig() *PluginScheduleConfig {
	if a.scheduleConfig != nil {
		return a.scheduleConfig
	}
	return &PluginScheduleConfig{
		Schedule: types.ScheduleConfig{
			Type:     types.CronSchedule,
			CronExpr: "0 0 9 * * *", // 默认每天9点
		},
		Enabled:        true,
		RunImmediately: false,
	}
}

// GenerateContent 实现新接口 - 调用旧版插件的方法
func (a *LegacyPluginAdapter) GenerateContent(ctx context.Context) (*PluginRuntimeContent, error) {
	// 调用旧版插件的GetPluginConfig方法
	config := a.legacyPlugin.GetPluginConfig(ctx)
	if config == nil || config.TaskConfig == nil {
		return &PluginRuntimeContent{
			MessageTypes: []types.MessageType{},
		}, nil
	}
	
	taskConfig := config.TaskConfig
	
	// 提取内容信息
	content := &PluginRuntimeContent{
		MessageTypes: taskConfig.MessageTypes,
	}
	
	// 提取邮件内容
	if taskConfig.Email != nil {
		content.Title = taskConfig.Email.Subject
		content.EmailContent = taskConfig.Email.Body
		content.EmailConfig = taskConfig.Email
	}
	
	// 提取钉钉内容
	if taskConfig.DingTalk != nil {
		if content.Title == "" {
			content.Title = taskConfig.DingTalk.Title
		}
		content.DingTalkContent = taskConfig.DingTalk.Text
		content.DingTalkConfig = taskConfig.DingTalk
	}
	
	return content, nil
}

// NewLegacyBasePlugin 创建旧版插件包装器（向后兼容）
func NewLegacyBasePlugin(legacyPlugin LegacyBasePlugin) *BasePluginWrapper {
	adapter := NewLegacyAdapter(legacyPlugin)
	return NewBasePlugin(adapter)
}

// 便捷函数：自动检测插件类型并创建合适的包装器
func NewAutoPlugin(plugin interface{}) *BasePluginWrapper {
	// 检查是否是新版插件
	if newPlugin, ok := plugin.(BasePlugin); ok {
		return NewBasePlugin(newPlugin)
	}
	
	// 检查是否是旧版插件
	if legacyPlugin, ok := plugin.(LegacyBasePlugin); ok {
		return NewLegacyBasePlugin(legacyPlugin)
	}
	
	// 如果都不是，返回nil
	return nil
}

// 迁移指南注释
/*
迁移指南：从旧版插件架构迁移到新版架构

1. 旧版插件结构：
   type OldPlugin struct{}
   func (p *OldPlugin) GetPluginConfig(ctx context.Context) *BasePluginConfig {
       // 在这里生成内容（问题：注册时就会执行）
       title, content := generateContent()
       return &BasePluginConfig{...}
   }

2. 新版插件结构：
   type NewPlugin struct{}
   func (p *NewPlugin) GetStaticInfo() *PluginStaticInfo {
       // 只返回静态信息，不生成内容
       return &PluginStaticInfo{...}
   }
   func (p *NewPlugin) GetScheduleConfig() *PluginScheduleConfig {
       // 只返回调度配置，不生成内容
       return &PluginScheduleConfig{...}
   }
   func (p *NewPlugin) GenerateContent(ctx context.Context) (*PluginRuntimeContent, error) {
       // 在这里生成内容（优势：只在执行时才生成）
       title, content := generateContent()
       return &PluginRuntimeContent{...}, nil
   }

3. 迁移步骤：
   a) 将GetPluginConfig方法拆分为三个方法
   b) 将内容生成逻辑移到GenerateContent方法中
   c) 使用NewBasePlugin而不是NewLegacyBasePlugin

4. 向后兼容：
   - 旧版插件可以继续使用NewLegacyBasePlugin包装
   - 系统会自动适配旧版插件到新架构
   - 建议逐步迁移到新架构以获得更好的性能和设计
*/
