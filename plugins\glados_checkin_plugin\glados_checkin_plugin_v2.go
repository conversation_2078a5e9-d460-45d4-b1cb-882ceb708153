package glados_checkin_plugin

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"taskReminder/plugin"
	"taskReminder/types"
)

// GladosCheckinPluginV2 GLaDOS签到插件 - 重构版本
// 采用新的插件架构，分离静态信息和动态内容生成
type GladosCheckinPluginV2 struct{}

// GetStaticInfo 获取插件静态信息（仅在注册时调用一次）
func (p *GladosCheckinPluginV2) GetStaticInfo() *plugin.PluginStaticInfo {
	return &plugin.PluginStaticInfo{
		PluginID:          "glados_checkin_v2",
		PluginDescription: "GLaDOS每日签到插件 - 重构版本（延迟执行）",
	}
}

// GetScheduleConfig 获取插件调度配置（仅在注册时调用一次）
func (p *GladosCheckinPluginV2) GetScheduleConfig() *plugin.PluginScheduleConfig {
	return &plugin.PluginScheduleConfig{
		Schedule: types.ScheduleConfig{
			Type:     types.CronSchedule,
			CronExpr: "0 5 9 * * *", // 每天9点执行一次
		},
		Enabled:        true,
		RunImmediately: false, // 设置为true可以立即测试
	}
}

// GenerateContent 生成运行时内容（每次执行时调用）
// 这里才是真正执行签到逻辑的地方
func (p *GladosCheckinPluginV2) GenerateContent(ctx context.Context) (*plugin.PluginRuntimeContent, error) {
	// 执行签到逻辑
	cookie := "koa:sess=eyJ1c2VySWQiOjM1MjkyOSwiX2V4cGlyZSI6MTc3OTY2ODgxOTE1OSwiX21heEFnZSI6MjU5MjAwMDAwMDB9; koa:sess.sig=68P-yOLOMgnRSQBbbhGUah73QTI"
	
	checkinTime := time.Now().Format("2006-01-02 15:04:05")
	result := p.gladosCheckin(cookie)

	// 根据签到结果生成内容
	if result.Success {
		title := "🎉 Glados 签到成功"
		dingTalkContent := fmt.Sprintf(`**签到时间**: %s

**签到结果**: ✅ 签到成功

**账户信息**:
- 剩余流量: %s
- 账户状态: %s
- 到期时间: %s

---
*🤖 自动签到服务 - 每日9点执行*`, 
			checkinTime, 
			result.LeftDays, 
			result.Status, 
			result.ExpireTime)

		emailContent := fmt.Sprintf(`
<h2>🎉 GLaDOS 签到成功</h2>
<p><strong>签到时间</strong>: %s</p>
<p><strong>签到结果</strong>: ✅ 签到成功</p>

<h3>📊 账户信息</h3>
<ul>
<li><strong>剩余流量</strong>: %s</li>
<li><strong>账户状态</strong>: %s</li>
<li><strong>到期时间</strong>: %s</li>
</ul>

<hr>
<p><em>🤖 自动签到服务 - 每日9点执行</em></p>
`, checkinTime, result.LeftDays, result.Status, result.ExpireTime)

		return &plugin.PluginRuntimeContent{
			Title:           title,
			DingTalkContent: dingTalkContent,
			EmailContent:    emailContent,
			MessageTypes:    []types.MessageType{types.DingTalkMessage, types.EmailMessage},
		}, nil
	} else {
		// 签到失败
		title := "❌ Glados 签到失败"
		dingTalkContent := fmt.Sprintf(`**签到时间**: %s

**签到结果**: ❌ 签到失败

**错误信息**: %s

---
*🤖 自动签到服务 - 请检查配置*`, checkinTime, result.Message)

		emailContent := fmt.Sprintf(`
<h2>❌ GLaDOS 签到失败</h2>
<p><strong>签到时间</strong>: %s</p>
<p><strong>签到结果</strong>: ❌ 签到失败</p>
<p><strong>错误信息</strong>: %s</p>

<hr>
<p><em>🤖 自动签到服务 - 请检查配置</em></p>
`, checkinTime, result.Message)

		return &plugin.PluginRuntimeContent{
			Title:           title,
			DingTalkContent: dingTalkContent,
			EmailContent:    emailContent,
			MessageTypes:    []types.MessageType{types.DingTalkMessage, types.EmailMessage},
		}, nil
	}
}

// CheckinResult 签到结果结构
type CheckinResult struct {
	Success    bool   `json:"success"`
	Message    string `json:"message"`
	LeftDays   string `json:"leftDays"`
	Status     string `json:"status"`
	ExpireTime string `json:"expireTime"`
}

// gladosCheckin 执行GLaDOS签到
func (p *GladosCheckinPluginV2) gladosCheckin(cookie string) CheckinResult {
	// 签到请求
	checkinURL := "https://glados.rocks/api/user/checkin"
	checkinData := map[string]string{"token": "glados.one"}
	
	jsonData, _ := json.Marshal(checkinData)
	req, err := http.NewRequest("POST", checkinURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("创建签到请求失败: %v", err)}
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Cookie", cookie)
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("签到请求失败: %v", err)}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("读取签到响应失败: %v", err)}
	}

	var checkinResp map[string]interface{}
	if err := json.Unmarshal(body, &checkinResp); err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("解析签到响应失败: %v", err)}
	}

	// 获取用户状态
	statusURL := "https://glados.rocks/api/user/status"
	statusReq, err := http.NewRequest("GET", statusURL, nil)
	if err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("创建状态请求失败: %v", err)}
	}

	statusReq.Header.Set("Cookie", cookie)
	statusReq.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	statusResp, err := client.Do(statusReq)
	if err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("状态请求失败: %v", err)}
	}
	defer statusResp.Body.Close()

	statusBody, err := io.ReadAll(statusResp.Body)
	if err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("读取状态响应失败: %v", err)}
	}

	var statusData map[string]interface{}
	if err := json.Unmarshal(statusBody, &statusData); err != nil {
		return CheckinResult{Success: false, Message: fmt.Sprintf("解析状态响应失败: %v", err)}
	}

	// 解析响应数据
	if data, ok := statusData["data"].(map[string]interface{}); ok {
		leftDays := "未知"
		status := "未知"
		expireTime := "未知"

		if ld, ok := data["leftDays"].(string); ok {
			leftDays = ld
		}
		if st, ok := data["vip"].(bool); ok {
			if st {
				status = "VIP用户"
			} else {
				status = "普通用户"
			}
		}
		if et, ok := data["days"].(float64); ok {
			expireTime = fmt.Sprintf("%.0f天后", et)
		}

		return CheckinResult{
			Success:    true,
			Message:    "签到成功",
			LeftDays:   leftDays,
			Status:     status,
			ExpireTime: expireTime,
		}
	}

	return CheckinResult{Success: false, Message: "解析用户状态失败"}
}
