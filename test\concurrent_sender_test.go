package test

import (
	"context"
	"log"
	"sync"
	"testing"
	"time"

	"taskReminder/sender"
	"taskReminder/types"
)

// 模拟发送器，用于测试并发性能
type MockSender struct {
	emailDelay    time.Duration
	dingtalkDelay time.Duration
	emailCalls    int
	dingtalkCalls int
	mu            sync.Mutex
}

func NewMockSender(emailDelay, dingtalkDelay time.Duration) *MockSender {
	return &MockSender{
		emailDelay:    emailDelay,
		dingtalkDelay: dingtalkDelay,
	}
}

func (m *MockSender) SendEmail(ctx context.Context, config *types.EmailConfig) error {
	m.mu.Lock()
	m.emailCalls++
	m.mu.Unlock()

	log.Printf("开始发送邮件，预计耗时: %v", m.emailDelay)
	time.Sleep(m.emailDelay)
	log.Printf("邮件发送完成")
	return nil
}

func (m *MockSender) SendDingTalk(ctx context.Context, config *types.DingTalkConfig) error {
	m.mu.Lock()
	m.dingtalkCalls++
	m.mu.Unlock()

	log.Printf("开始发送钉钉消息，预计耗时: %v", m.dingtalkDelay)
	time.Sleep(m.dingtalkDelay)
	log.Printf("钉钉消息发送完成")
	return nil
}

func (m *MockSender) GetStats() (int, int) {
	m.mu.Lock()
	defer m.mu.Unlock()
	return m.emailCalls, m.dingtalkCalls
}

// 测试并发发送功能
func TestConcurrentSending(t *testing.T) {
	log.Println("=== 测试并发消息发送功能 ===")

	// 创建统一发送器
	unifiedSender := sender.NewUnifiedSender()

	// 创建测试配置 - 同时发送邮件和钉钉消息
	config := &types.TaskConfig{
		Name:         "并发发送测试",
		MessageTypes: []types.MessageType{types.EmailMessage, types.DingTalkMessage},
		Email: &types.EmailConfig{
			SMTPHost: "smtp.test.com",
			SMTPPort: 587,
			Username: "<EMAIL>",
			Password: "test_password",
			From:     "<EMAIL>",
			To:       []string{"<EMAIL>"},
			Subject:  "并发测试邮件",
			Body:     "这是一封测试邮件，用于验证并发发送功能。",
		},
		DingTalk: &types.DingTalkConfig{
			WebhookURL: "https://oapi.dingtalk.com/robot/send?access_token=test_token",
			Title:      "并发测试消息",
			Text:       "这是一条测试钉钉消息，用于验证并发发送功能。",
		},
		Enabled: true,
	}

	ctx := context.Background()

	// 测试1：单个消息类型发送
	log.Println("\n--- 测试1：单个消息类型发送 ---")
	singleConfig := *config
	singleConfig.MessageTypes = []types.MessageType{types.EmailMessage}

	start := time.Now()
	err := unifiedSender.SendMessages(ctx, &singleConfig)
	duration := time.Since(start)

	if err != nil {
		log.Printf("单个消息发送测试失败: %v", err)
	} else {
		log.Printf("单个消息发送测试成功，耗时: %v", duration)
	}

	// 测试2：多个消息类型并发发送
	log.Println("\n--- 测试2：多个消息类型并发发送 ---")

	start = time.Now()
	err = unifiedSender.SendMessages(ctx, config)
	duration = time.Since(start)

	if err != nil {
		log.Printf("并发消息发送测试失败: %v", err)
	} else {
		log.Printf("并发消息发送测试成功，耗时: %v", duration)
	}

	// 测试3：空消息类型列表
	log.Println("\n--- 测试3：空消息类型列表 ---")
	emptyConfig := *config
	emptyConfig.MessageTypes = []types.MessageType{}

	start = time.Now()
	err = unifiedSender.SendMessages(ctx, &emptyConfig)
	duration = time.Since(start)

	if err != nil {
		log.Printf("空消息类型测试失败: %v", err)
	} else {
		log.Printf("空消息类型测试成功，耗时: %v", duration)
	}

	log.Println("\n=== 并发发送测试完成 ===")
}

// 性能对比测试
func TestPerformanceComparison(t *testing.T) {
	log.Println("=== 性能对比测试：顺序 vs 并发 ===")

	// 模拟较长的发送时间来突出并发优势
	emailDelay := 2 * time.Second
	dingtalkDelay := 1 * time.Second

	log.Printf("模拟发送时间 - 邮件: %v, 钉钉: %v", emailDelay, dingtalkDelay)
	log.Printf("理论上顺序发送总时间: %v", emailDelay+dingtalkDelay)
	log.Printf("理论上并发发送总时间: %v", max(emailDelay, dingtalkDelay))

	// 创建统一发送器
	unifiedSender := sender.NewUnifiedSender()

	// 创建测试配置
	config := &types.TaskConfig{
		Name:         "性能对比测试",
		MessageTypes: []types.MessageType{types.EmailMessage, types.DingTalkMessage},
		Email: &types.EmailConfig{
			SMTPHost: "smtp.test.com",
			SMTPPort: 587,
			Username: "<EMAIL>",
			Password: "test_password",
			From:     "<EMAIL>",
			To:       []string{"<EMAIL>"},
			Subject:  "性能测试邮件",
			Body:     "这是一封性能测试邮件。",
		},
		DingTalk: &types.DingTalkConfig{
			WebhookURL: "https://oapi.dingtalk.com/robot/send?access_token=test_token",
			Title:      "性能测试消息",
			Text:       "这是一条性能测试钉钉消息。",
		},
		Enabled: true,
	}

	ctx := context.Background()

	// 测试并发发送
	log.Println("\n--- 执行并发发送测试 ---")
	start := time.Now()
	err := unifiedSender.SendMessages(ctx, config)
	concurrentDuration := time.Since(start)

	if err != nil {
		log.Printf("并发发送失败: %v", err)
	} else {
		log.Printf("并发发送成功，实际耗时: %v", concurrentDuration)
	}

	// 计算性能提升
	expectedSequentialTime := emailDelay + dingtalkDelay
	expectedConcurrentTime := max(emailDelay, dingtalkDelay)

	log.Printf("\n=== 性能分析 ===")
	log.Printf("预期顺序发送时间: %v", expectedSequentialTime)
	log.Printf("预期并发发送时间: %v", expectedConcurrentTime)
	log.Printf("实际并发发送时间: %v", concurrentDuration)

	if concurrentDuration < expectedSequentialTime {
		improvement := float64(expectedSequentialTime-concurrentDuration) / float64(expectedSequentialTime) * 100
		log.Printf("性能提升: %.1f%%", improvement)
	}

	log.Println("\n=== 性能对比测试完成 ===")
}

// 辅助函数：返回两个时间间隔中的较大值
func max(a, b time.Duration) time.Duration {
	if a > b {
		return a
	}
	return b
}

// 基准测试
func BenchmarkConcurrentSending(b *testing.B) {
	unifiedSender := sender.NewUnifiedSender()

	config := &types.TaskConfig{
		MessageTypes: []types.MessageType{types.EmailMessage, types.DingTalkMessage},
		Email: &types.EmailConfig{
			SMTPHost: "smtp.test.com",
			SMTPPort: 587,
			Username: "<EMAIL>",
			Password: "test_password",
			From:     "<EMAIL>",
			To:       []string{"<EMAIL>"},
			Subject:  "基准测试邮件",
			Body:     "基准测试内容",
		},
		DingTalk: &types.DingTalkConfig{
			WebhookURL: "https://oapi.dingtalk.com/robot/send?access_token=test_token",
			Title:      "基准测试消息",
			Text:       "基准测试内容",
		},
		Enabled: true,
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = unifiedSender.SendMessages(ctx, config)
	}
}
