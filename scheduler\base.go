package scheduler

import (
	"context"
	"fmt"
	"log"
	"sync"

	"taskReminder/sender"
	"taskReminder/types"

	"github.com/robfig/cron/v3"
)

// 任务执行回调函数类型
type TaskExecuteFunc func(ctx context.Context, taskID string) (*types.TaskConfig, error)

// 任务调度器
type TaskScheduler struct {
	cron             *cron.Cron
	sender           *sender.UnifiedSender
	tasks            map[string]*types.TaskConfig
	intervals        map[string]*IntervalTask
	lunarTasks       map[string]*LunarTask
	executeCallbacks map[string]TaskExecuteFunc // 任务执行回调
	ctx              context.Context
	cancel           context.CancelFunc
	mu               sync.RWMutex
	running          bool
	logger           *log.Logger
}

// 创建新的任务调度器
func NewTaskScheduler() *TaskScheduler {
	return &TaskScheduler{
		cron:             cron.New(cron.WithSeconds()),
		sender:           sender.NewUnifiedSender(),
		tasks:            make(map[string]*types.TaskConfig),
		intervals:        make(map[string]*IntervalTask),
		lunarTasks:       make(map[string]*LunarTask),
		executeCallbacks: make(map[string]TaskExecuteFunc),
		logger:           log.Default(),
	}
}

// 设置日志记录器
func (s *TaskScheduler) SetLogger(logger *log.Logger) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.logger = logger
}

// 设置任务执行回调
func (s *TaskScheduler) SetTaskExecuteCallback(taskID string, callback TaskExecuteFunc) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.executeCallbacks[taskID] = callback
}

// 添加任务
func (s *TaskScheduler) AddTask(taskID string, config *types.TaskConfig) error {
	if config == nil {
		return ErrTaskConfigNil
	}

	if !config.Enabled {
		s.logger.Printf("任务 %s 已禁用，跳过添加", taskID)
		return nil
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	// 如果任务已存在，先移除
	if _, exists := s.tasks[taskID]; exists {
		s.removeTaskLocked(taskID)
	}

	s.tasks[taskID] = config

	switch config.Schedule.Type {
	case types.CronSchedule:
		return s.addCronTask(taskID, config)
	case types.IntervalSchedule:
		return s.addIntervalTask(taskID, config)
	case types.LunarSchedule:
		return s.addLunarTask(taskID, config)
	default:
		return ErrUnsupportedScheduleType
	}
}

// 移除任务
func (s *TaskScheduler) RemoveTask(taskID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	return s.removeTaskLocked(taskID)
}

// 移除任务（内部方法，需要持有锁）
func (s *TaskScheduler) removeTaskLocked(taskID string) error {
	// 移除间隔任务
	if intervalTask, exists := s.intervals[taskID]; exists {
		close(intervalTask.stopChan)
		if intervalTask.ticker != nil {
			intervalTask.ticker.Stop()
		}
		delete(s.intervals, taskID)
	}

	// 移除农历任务
	if lunarTask, exists := s.lunarTasks[taskID]; exists {
		close(lunarTask.stopChan)
		delete(s.lunarTasks, taskID)
	}

	// 从任务列表中移除
	delete(s.tasks, taskID)

	s.logger.Printf("移除任务: %s", taskID)
	return nil
}

// 启动调度器
func (s *TaskScheduler) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return ErrSchedulerAlreadyRunning
	}

	s.ctx, s.cancel = context.WithCancel(ctx)
	s.cron.Start()
	s.running = true

	s.logger.Println("任务调度器启动成功")
	return nil
}

// 停止调度器
func (s *TaskScheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return ErrSchedulerNotRunning
	}

	// 停止cron调度器
	s.cron.Stop()

	// 停止所有间隔任务
	for taskID, intervalTask := range s.intervals {
		close(intervalTask.stopChan)
		if intervalTask.ticker != nil {
			intervalTask.ticker.Stop()
		}
		delete(s.intervals, taskID)
	}

	// 停止所有农历任务
	for taskID, lunarTask := range s.lunarTasks {
		close(lunarTask.stopChan)
		delete(s.lunarTasks, taskID)
	}

	// 取消上下文
	if s.cancel != nil {
		s.cancel()
	}

	s.running = false
	s.logger.Println("任务调度器已停止")
	return nil
}

// 执行任务
func (s *TaskScheduler) executeTask(taskID string, config *types.TaskConfig) {
	// 使用Description字段提供更详细的日志信息
	s.logger.Printf("开始执行任务: %s (%s)", taskID, config.Name)
	ctx := context.Background()
	if s.ctx != nil {
		ctx = s.ctx
	}

	// 获取运行时配置
	runtimeConfig := config
	s.mu.RLock()
	if callback, exists := s.executeCallbacks[taskID]; exists {
		s.mu.RUnlock()
		if rc, err := callback(ctx, taskID); err == nil && rc != nil {
			runtimeConfig = rc
		} else if err != nil {
			s.logger.Printf("获取任务 %s 运行时配置失败: %v", taskID, err)
			return
		}
	} else {
		s.mu.RUnlock()
	}

	// 检查是否有消息类型需要发送，如果没有则跳过发送
	if len(runtimeConfig.MessageTypes) == 0 {
		s.logger.Printf("任务 %s (%s) 无需发送消息", taskID, config.Name)
		return
	}

	// 发送消息
	if err := s.sender.SendMessages(ctx, runtimeConfig); err != nil {
		s.logger.Printf("任务 %s (%s) 发送消息失败: %v", taskID, config.Name, err)
	} else {
		s.logger.Printf("任务 %s (%s) 发送消息成功", taskID, config.Name)
	}
}

// 立即执行任务（用于测试插件）
func (s *TaskScheduler) ExecuteTaskImmediately(taskID string, config *types.TaskConfig) error {
	if config == nil {
		return ErrTaskConfigNil
	}

	s.logger.Printf("立即执行任务: %s (%s)", taskID, config.Name)

	// 在新的goroutine中执行任务，避免阻塞
	go s.executeTask(taskID, config)

	return nil
}

// 获取任务状态信息
func (s *TaskScheduler) GetTaskStatus() map[string]string {
	s.mu.RLock()
	defer s.mu.RUnlock()

	status := make(map[string]string)
	for taskID, config := range s.tasks {
		status[taskID] = config.Name
	}
	return status
}

// 列出所有任务信息
func (s *TaskScheduler) ListTasks() {
	s.mu.RLock()
	defer s.mu.RUnlock()

	s.logger.Println("当前注册的任务:")
	for taskID, config := range s.tasks {
		scheduleInfo := ""
		switch config.Schedule.Type {
		case types.CronSchedule:
			scheduleInfo = fmt.Sprintf("Cron: %s", config.Schedule.CronExpr)
		case types.IntervalSchedule:
			scheduleInfo = fmt.Sprintf("间隔: 每%d%s", config.Schedule.Interval, config.Schedule.Unit)
		case types.LunarSchedule:
			scheduleInfo = fmt.Sprintf("农历: %s", config.Schedule.LunarCron)
		}

		status := "启用"
		if !config.Enabled {
			status = "禁用"
		}

		s.logger.Printf("  - %s: %s (%s) [%s]", taskID, config.Name, scheduleInfo, status)
	}
}
