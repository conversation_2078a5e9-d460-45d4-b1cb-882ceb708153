package plugin

import (
	"context"
	"fmt"
	"log"
	"sync"

	"taskReminder/scheduler"
	"taskReminder/types"
)

// 插件管理器
type Manager struct {
	plugins   map[string]types.Plugin
	scheduler *scheduler.TaskScheduler
	mu        sync.RWMutex
}

// 创建新的插件管理器
func NewManager() *Manager {
	return &Manager{
		plugins:   make(map[string]types.Plugin),
		scheduler: scheduler.NewTaskScheduler(),
	}
}

// 注册插件
func (m *Manager) RegisterPlugin(plugin types.Plugin) error {
	if plugin == nil {
		return fmt.Errorf("插件不能为空")
	}

	name := plugin.GetName()
	if name == "" {
		return fmt.Errorf("插件名称不能为空")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	// 检查插件是否已存在
	if _, exists := m.plugins[name]; exists {
		return fmt.Errorf("插件 %s 已存在", name)
	}

	// 注册插件
	m.plugins[name] = plugin

	// 获取任务配置并添加到调度器
	config := plugin.GetTaskConfig()
	if config != nil {
		if err := m.scheduler.AddTask(name, config); err != nil {
			// 如果添加任务失败，移除插件
			delete(m.plugins, name)
			return fmt.Errorf("添加任务失败: %v", err)
		}

		// 设置任务执行回调，用于获取运行时配置
		m.scheduler.SetTaskExecuteCallback(name, func(ctx context.Context, taskID string) (*types.TaskConfig, error) {
			return plugin.GetRuntimeConfig(ctx), nil
		})

		// 检查是否需要立即执行（用于测试插件）
		if config.RunImmediately {
			log.Printf("插件 %s 设置为立即执行，开始执行...", name)
			go func() {
				ctx := context.Background()
				runtimeConfig := plugin.GetRuntimeConfig(ctx)
				if err := m.scheduler.ExecuteTaskImmediately(name, runtimeConfig); err != nil {
					log.Printf("立即执行插件 %s 失败: %v", name, err)
				} else {
					log.Printf("立即执行插件 %s 成功", name)
				}
			}()
		}
	}

	log.Printf("插件注册成功: %s - %s", name, plugin.GetDescription())
	return nil
}

// 批量注册插件 - 优雅的解决方案
func (m *Manager) RegisterPlugins(plugins ...types.Plugin) {
	log.Printf("🔧 开始批量注册 %d 个插件...", len(plugins))

	successCount := 0
	for _, plugin := range plugins {
		if plugin == nil {
			log.Printf("❌ 跳过空插件")
			continue
		}

		if err := m.RegisterPlugin(plugin); err != nil {
			log.Printf("❌ 注册插件 %s 失败: %v", plugin.GetName(), err)
		} else {
			successCount++
		}
	}

	log.Printf("✅ 批量注册完成，成功注册 %d/%d 个插件", successCount, len(plugins))
}

// 注销插件
func (m *Manager) UnregisterPlugin(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.plugins[name]; !exists {
		return fmt.Errorf("插件 %s 不存在", name)
	}

	// 从调度器中移除任务
	if err := m.scheduler.RemoveTask(name); err != nil {
		log.Printf("移除任务失败: %v", err)
	}

	// 移除插件
	delete(m.plugins, name)

	log.Printf("插件注销成功: %s", name)
	return nil
}

// 获取插件
func (m *Manager) GetPlugin(name string) (types.Plugin, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	plugin, exists := m.plugins[name]
	return plugin, exists
}

// 获取所有插件
func (m *Manager) GetAllPlugins() map[string]types.Plugin {
	m.mu.RLock()
	defer m.mu.RUnlock()

	result := make(map[string]types.Plugin)
	for name, plugin := range m.plugins {
		result[name] = plugin
	}
	return result
}

// 启动插件管理器
func (m *Manager) Start(ctx context.Context) error {
	log.Println("启动插件管理器...")

	// 启动调度器
	if err := m.scheduler.Start(ctx); err != nil {
		return fmt.Errorf("启动调度器失败: %v", err)
	}

	// 执行所有插件的初始化回调
	m.mu.RLock()
	for name, plugin := range m.plugins {
		if err := plugin.OnExecute(ctx); err != nil {
			log.Printf("插件 %s 初始化失败: %v", name, err)
		}
	}
	m.mu.RUnlock()

	log.Println("插件管理器启动成功")
	return nil
}

// 停止插件管理器
func (m *Manager) Stop() error {
	log.Println("停止插件管理器...")

	// 停止调度器
	if err := m.scheduler.Stop(); err != nil {
		return fmt.Errorf("停止调度器失败: %v", err)
	}

	log.Println("插件管理器已停止")
	return nil
}

// 列出所有插件信息
func (m *Manager) ListPlugins() {
	m.mu.RLock()
	defer m.mu.RUnlock()

	log.Println("已注册的插件:")
	for name, plugin := range m.plugins {
		config := plugin.GetTaskConfig()
		status := "禁用"
		if config != nil && config.Enabled {
			status = "启用"
		}
		log.Printf("  - %s: %s [%s]", name, plugin.GetDescription(), status)
	}
}
