package plugin

import "taskReminder/types"

// 邮箱配置变量
var SinaEmailConfig = &types.EmailConfig{
	SMTPHost: "smtp.sina.com",
	SMTPPort: 465,
	Username: "<EMAIL>",
	Password: "18dfe63a7516fa9f",
	From:     "<EMAIL>",
	FromName: "Yanjf邮箱订阅",
}

var ResendEmailConfig = &types.EmailConfig{
	SMTPHost: "smtp.resend.com",
	SMTPPort: 465,
	Username: "resend",
	Password: "re_G55vYtmP_Lr7dJg1aiFpn1PgGaRwN4qjZ",
	From:     "<EMAIL>",
	FromName: "Resend域名邮箱订阅",
}

// 钉钉配置变量
var DingTalkConfig = &types.DingTalkConfig{
	WebhookURL: "https://oapi.dingtalk.com/robot/send?access_token=7debd389741b51484bf8e10726e397269e9896139496e9cba0f11f06b0cab423",
	Secret:     "SECdfe010745044c45132014d2f13476cf29792396b572429e73901cd4572c3a63d",
}
