package glados_checkin_plugin

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"taskReminder/plugin"
	"taskReminder/types"
)

// GladosCheckinPlugin GLaDOS签到插件
type GladosCheckinPlugin struct{}

// GetPluginConfig 获取插件配置（注册时返回静态配置，执行时动态生成内容）
func (p *GladosCheckinPlugin) GetPluginConfig(ctx context.Context) *plugin.BasePluginConfig {
	// 检查是否为运行时调用（有实际的context）
	isRuntimeCall := ctx != context.Background()

	var title, dingTalkContent, emailContent string
	var messageTypes []types.MessageType
	var enabled bool

	if isRuntimeCall {
		// 运行时调用：执行签到并生成动态内容
		title, dingTalkContent, emailContent = p.generateTitleAndContent()

		// 根据content是否为空决定MessageTypes
		if dingTalkContent != "" {
			messageTypes = append(messageTypes, types.DingTalkMessage)
		}
		if emailContent != "" {
			messageTypes = append(messageTypes, types.EmailMessage)
		}

		// 只有当内容不为空时才启用任务
		enabled = dingTalkContent != "" || emailContent != ""
	} else {
		// 注册时调用：返回静态配置，不执行签到
		title = "GLaDOS每日签到"
		dingTalkContent = "签到任务配置"
		emailContent = ""
		messageTypes = []types.MessageType{types.DingTalkMessage}
		enabled = true
	}

	return &plugin.BasePluginConfig{
		// 基本信息
		PluginID:          "glados_checkin",
		PluginDescription: "GLaDOS签到插件 - 每日自动签到并发送通知",

		// 完整的任务配置 - 包含动态生成的消息内容
		TaskConfig: &types.TaskConfig{
			Name: "GLaDOS每日签到",
			Schedule: types.ScheduleConfig{
				Type:     types.CronSchedule,
				CronExpr: "0 5 9 * * *", // 每天9点执行一次
			},
			MessageTypes:   messageTypes,
			RunImmediately: false,
			// 邮件配置
			Email: &types.EmailConfig{
				SMTPHost: plugin.SinaEmailConfig.SMTPHost,
				SMTPPort: plugin.SinaEmailConfig.SMTPPort,
				Username: plugin.SinaEmailConfig.Username,
				Password: plugin.SinaEmailConfig.Password,
				From:     plugin.SinaEmailConfig.From,
				FromName: plugin.SinaEmailConfig.FromName,
				To:       []string{"<EMAIL>"}, // 收件人从环境变量获取
				Subject:  title,                     // 邮件标题
				Body:     emailContent,              // 邮件内容
				IsHTML:   true,                      // 使用HTML格式
			},
			// 钉钉配置
			DingTalk: &types.DingTalkConfig{
				WebhookURL: plugin.DingTalkConfig.WebhookURL,
				Secret:     plugin.DingTalkConfig.Secret,
				AtMobiles:  []string{},
				AtAll:      false,
				Title:      title,                  // 钉钉标题
				Text:       dingTalkContent,        // 钉钉内容
				MsgType:    types.DingTalkMarkdown, // 使用Markdown格式
			},
			Enabled: enabled,
		},
	}
}

// GladosCheckinResponse GLaDOS签到响应结构
type GladosCheckinResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	List    []struct {
		Balance any `json:"balance"`
	} `json:"list"`
}

// gladosCheckinResult GLaDOS签到结果
type gladosCheckinResult struct {
	Success   bool
	Message   string
	DaysLeft  any
	ErrorCode string
}

// gladosCheckin 执行GLaDOS签到
func (p *GladosCheckinPlugin) gladosCheckin(cookie string) *gladosCheckinResult {
	url := "https://glados.rocks/api/user/checkin"

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 创建请求数据
	data := map[string]string{"token": "glados.one"}
	jsonData, err := json.Marshal(data)
	if err != nil {
		return &gladosCheckinResult{
			Success: false,
			Message: fmt.Sprintf("序列化请求数据失败: %v", err),
		}
	}

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return &gladosCheckinResult{
			Success: false,
			Message: fmt.Sprintf("创建请求失败: %v", err),
		}
	}

	// 设置请求头
	req.Header.Set("cookie", cookie)
	req.Header.Set("content-type", "application/json")
	req.Header.Set("referer", "https://glados.rocks/console/checkin")
	req.Header.Set("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return &gladosCheckinResult{
			Success: false,
			Message: fmt.Sprintf("发送请求失败: %v", err),
		}
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return &gladosCheckinResult{
			Success: false,
			Message: fmt.Sprintf("读取响应失败: %v", err),
		}
	}

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return &gladosCheckinResult{
			Success: false,
			Message: fmt.Sprintf("服务器响应错误: %d, 响应内容: %s", resp.StatusCode, string(body)),
		}
	}

	// 解析JSON响应
	var result GladosCheckinResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return &gladosCheckinResult{
			Success: false,
			Message: fmt.Sprintf("解析响应JSON失败: %v, 响应内容: %s", err, string(body)),
		}
	}

	// 检查响应格式是否包含必需字段
	if result.Message == "" {
		return &gladosCheckinResult{
			Success: false,
			Message: fmt.Sprintf("无效的响应格式，缺少message字段: %s", string(body)),
		}
	}

	// 获取剩余天数
	var daysLeft any
	if len(result.List) > 0 {
		daysLeft = result.List[0].Balance
	}

	return &gladosCheckinResult{
		Success:  true,
		Message:  result.Message,
		DaysLeft: daysLeft,
	}
}

// ServerChanResponse Server酱响应结构
type ServerChanResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

// generateTitleAndContent 生成标题和内容
func (p *GladosCheckinPlugin) generateTitleAndContent() (title, dingTalkContent, emailContent string) {
	// 获取环境变量
	cookie := "koa:sess=eyJ1c2VySWQiOjM1MjkyOSwiX2V4cGlyZSI6MTc3OTY2ODgxOTE1OSwiX21heEFnZSI6MjU5MjAwMDAwMDB9; koa:sess.sig=68P-yOLOMgnRSQBbbhGUah73QTI"

	// 执行签到
	checkinTime := time.Now().Format("2006-01-02 15:04:05")
	result := p.gladosCheckin(cookie)

	// 准备通知内容
	if result.Success {
		title = "🎉 Glados 签到成功 "
		dingTalkContent = fmt.Sprintf(`**签到时间**: %s

**签到结果**: %s

**剩余天数**: %v

---
*系统自动发送，请勿回复*`, checkinTime, result.Message, result.DaysLeft)
		emailContent = ""
		// 		emailContent = fmt.Sprintf(`<!DOCTYPE html>
		// <html>
		// <head>
		//     <meta charset="utf-8">
		//     <title>%s</title>
		// </head>
		// <body>
		//     <h1>🎉 Glados 签到成功</h1>
		//     <p><strong>签到时间</strong>: %s</p>
		//     <p><strong>签到结果</strong>: %s</p>
		//     <p><strong>剩余天数</strong>: %v</p>
		//     <p><em>系统自动发送，请勿回复</em></p>
		// </body>
		// </html>`, title, checkinTime, result.Message, result.DaysLeft)
	} else {
		title = fmt.Sprintf("❌ Glados 签到失败 - %s", checkinTime)
		dingTalkContent = fmt.Sprintf(`### ❌ Glados 签到失败

**签到时间**: %s

**错误信息**: %s

---
*系统自动发送，请勿回复*`, checkinTime, result.Message)
		emailContent = ""
		// 		emailContent = fmt.Sprintf(`<!DOCTYPE html>
		// <html>
		// <head>
		//     <meta charset="utf-8">
		//     <title>%s</title>
		// </head>
		// <body>
		//     <h1>❌ Glados 签到失败</h1>
		//     <p><strong>签到时间</strong>: %s</p>
		//     <p><strong>错误信息</strong>: %s</p>
		//     <p><em>系统自动发送，请勿回复</em></p>
		// </body>
		// </html>`, title, checkinTime, result.Message)
	}

	return title, dingTalkContent, emailContent
}
