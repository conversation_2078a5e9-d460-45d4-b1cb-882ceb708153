package sender

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"time"

	"taskReminder/types"
)

// 钉钉消息发送器
type DingTalkSender struct{}

// 钉钉消息结构
type DingTalkMessage struct {
	MsgType  string           `json:"msgtype"`
	Text     *TextContent     `json:"text,omitempty"`     // 文本消息内容
	Markdown *MarkdownContent `json:"markdown,omitempty"` // Markdown消息内容
	At       struct {
		AtMobiles []string `json:"atMobiles"`
		IsAtAll   bool     `json:"isAtAll"`
	} `json:"at"`
}

// 文本消息内容
type TextContent struct {
	Content string `json:"content"`
}

// Markdown消息内容
type MarkdownContent struct {
	Title string `json:"title"`
	Text  string `json:"text"`
}

// 创建新的钉钉发送器
func NewDingTalkSender() *DingTalkSender {
	return &DingTalkSender{}
}

// 发送钉钉消息
func (d *DingTalkSender) SendDingTalk(ctx context.Context, config *types.DingTalkConfig) error {
	if config == nil {
		return fmt.Errorf("钉钉配置不能为空")
	}

	// 验证必要参数
	if config.WebhookURL == "" {
		return fmt.Errorf("钉钉Webhook地址不能为空")
	}
	if config.Text == "" {
		return fmt.Errorf("消息内容为空")
	}

	// 设置默认消息类型为markdown
	msgType := config.MsgType
	if msgType == "" {
		msgType = types.DingTalkMarkdown
	}

	log.Printf("开始发送钉钉消息 (%s): %s", msgType, config.Title)

	// 创建消息结构
	message := DingTalkMessage{
		MsgType: string(msgType),
	}
	message.At.AtMobiles = config.AtMobiles
	message.At.IsAtAll = config.AtAll

	// 根据消息类型构建内容
	switch msgType {
	case types.DingTalkText:
		// 文本消息
		content := config.Text
		if config.Title != "" {
			content = fmt.Sprintf("%s\n\n%s", config.Title, config.Text)
		}
		message.Text = &TextContent{
			Content: content,
		}
	case types.DingTalkMarkdown:
		// Markdown消息
		title := config.Title
		if title == "" {
			title = "消息通知"
		}
		content := config.Text
		if config.Title != "" {
			content = fmt.Sprintf("## %s\n\n%s", config.Title, config.Text)
		}
		message.Markdown = &MarkdownContent{
			Title: title,
			Text:  content,
		}
	default:
		return fmt.Errorf("不支持的消息类型: %s", msgType)
	}

	// 序列化消息
	messageBytes, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %v", err)
	}

	// 构建请求URL（包含签名）
	requestURL := config.WebhookURL
	if config.Secret != "" {
		timestamp := time.Now().UnixNano() / 1e6
		sign := d.generateSign(timestamp, config.Secret)
		requestURL = fmt.Sprintf("%s&timestamp=%d&sign=%s", config.WebhookURL, timestamp, sign)
	}

	// 发送HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", requestURL, bytes.NewBuffer(messageBytes))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("钉钉API返回错误状态: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查是否成功
	if errCode, ok := result["errcode"].(float64); ok && errCode != 0 {
		errMsg := result["errmsg"].(string)
		log.Printf("钉钉消息发送失败: %s", errMsg)
		return fmt.Errorf("钉钉消息发送失败: %s", errMsg)
	}

	log.Printf("钉钉消息发送成功: %s", config.Title)
	return nil
}

// 生成钉钉签名
func (d *DingTalkSender) generateSign(timestamp int64, secret string) string {
	stringToSign := fmt.Sprintf("%d\n%s", timestamp, secret)
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(stringToSign))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return url.QueryEscape(signature)
}

// 发送邮件（DingTalkSender不实现此方法）
func (d *DingTalkSender) SendEmail(ctx context.Context, config *types.EmailConfig) error {
	return fmt.Errorf("DingTalkSender不支持发送邮件")
}
