package scheduler

import (
	"fmt"
	"time"

	"taskReminder/types"
)

// 间隔任务
type IntervalTask struct {
	config   *types.TaskConfig
	ticker   *time.Ticker
	stopChan chan struct{}
}

// 添加间隔任务
func (s *TaskScheduler) addIntervalTask(taskID string, config *types.TaskConfig) error {
	if config.Schedule.Interval <= 0 {
		return ErrIntervalInvalid
	}

	duration, err := s.parseDuration(config.Schedule.Interval, config.Schedule.Unit)
	if err != nil {
		return fmt.Errorf("解析间隔时间失败: %v", err)
	}

	// 计算首次执行时间
	var firstRun time.Time
	if config.Schedule.StartTime != nil {
		firstRun = *config.Schedule.StartTime
	} else {
		firstRun = time.Now().Add(duration)
	}

	// 如果首次执行时间在过去，调整到下一个周期
	now := time.Now()
	if firstRun.Before(now) {
		elapsed := now.Sub(firstRun)
		cycles := elapsed / duration
		// 计算下一个执行时间点
		nextCycles := int64(cycles) + 1
		firstRun = firstRun.Add(time.Duration(nextCycles) * duration)

		// 如果计算出的时间仍然在过去（由于精度问题），再加一个周期
		if firstRun.Before(now) {
			firstRun = firstRun.Add(duration)
		}
	}

	intervalTask := &IntervalTask{
		config:   config,
		stopChan: make(chan struct{}),
	}

	s.intervals[taskID] = intervalTask

	// 启动间隔任务协程
	go s.runIntervalTask(taskID, intervalTask, firstRun, duration)

	s.logger.Printf("添加间隔任务成功: %s (每%d%s，首次执行: %s)",
		taskID, config.Schedule.Interval, config.Schedule.Unit, firstRun.Format("2006-01-02 15:04:05"))
	return nil
}

// 运行间隔任务
func (s *TaskScheduler) runIntervalTask(taskID string, task *IntervalTask, firstRun time.Time, duration time.Duration) {
	// 等待到首次执行时间
	if delay := time.Until(firstRun); delay > 0 {
		select {
		case <-time.After(delay):
		case <-task.stopChan:
			return
		}
	}

	// 首次执行
	s.executeTask(taskID, task.config)

	// 创建定时器
	task.ticker = time.NewTicker(duration)
	defer task.ticker.Stop()

	for {
		select {
		case <-task.ticker.C:
			s.executeTask(taskID, task.config)
		case <-task.stopChan:
			return
		}
	}
}

// 解析时间间隔
func (s *TaskScheduler) parseDuration(interval int, unit types.IntervalUnit) (time.Duration, error) {
	switch unit {
	case types.Second:
		return time.Duration(interval) * time.Second, nil
	case types.Minute:
		return time.Duration(interval) * time.Minute, nil
	case types.Hour:
		return time.Duration(interval) * time.Hour, nil
	case types.Day:
		return time.Duration(interval) * 24 * time.Hour, nil
	case types.Week:
		return time.Duration(interval) * 7 * 24 * time.Hour, nil
	case types.Month:
		return time.Duration(interval) * 30 * 24 * time.Hour, nil // 近似值
	case types.Year:
		return time.Duration(interval) * 365 * 24 * time.Hour, nil // 近似值
	default:
		return 0, fmt.Errorf("%w: %s", ErrUnsupportedTimeUnit, unit)
	}
}
