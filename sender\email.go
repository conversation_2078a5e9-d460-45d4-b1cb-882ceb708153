package sender

import (
	"context"
	"crypto/tls"
	"fmt"
	"log"

	"taskReminder/types"

	"gopkg.in/gomail.v2"
)

// 邮件发送器
type EmailSender struct{}

// 创建新的邮件发送器
func NewEmailSender() *EmailSender {
	return &EmailSender{}
}

// 发送邮件
func (e *EmailSender) SendEmail(ctx context.Context, config *types.EmailConfig) error {
	if config == nil {
		return fmt.Errorf("邮件配置不能为空")
	}

	// 使用EmailConfig的Validate方法进行验证
	if err := config.Validate(); err != nil {
		return fmt.Errorf("邮件配置验证失败: %v", err)
	}

	log.Printf("开始发送邮件: %s -> %v", config.Subject, config.To)

	// 创建邮件消息
	m := gomail.NewMessage()

	// 设置发件人
	if config.FromName != "" {
		m.SetAddressHeader("From", config.From, config.FromName)
	} else {
		m.SetHeader("From", config.From)
	}

	// 设置回复地址（如果配置了回复地址）
	if config.ReplyTo != "" {
		if config.ReplyToName != "" {
			m.SetAddressHeader("Reply-To", config.ReplyTo, config.ReplyToName)
		} else {
			m.SetHeader("Reply-To", config.ReplyTo)
		}
	}

	// 设置收件人
	m.SetHeader("To", config.To...)

	// 设置抄送人（CC）
	if len(config.CC) > 0 {
		m.SetHeader("Cc", config.CC...)
	}

	// 设置密送人（BCC）
	if len(config.BCC) > 0 {
		m.SetHeader("Bcc", config.BCC...)
	}

	// 设置主题
	if config.Subject != "" {
		m.SetHeader("Subject", config.Subject)
	} else {
		m.SetHeader("Subject", "定时提醒消息")
	}

	// 设置邮件优先级
	if config.Priority > 0 && config.Priority <= 5 {
		var priority string
		switch config.Priority {
		case 1:
			priority = "1 (Highest)"
		case 2:
			priority = "2 (High)"
		case 3:
			priority = "3 (Normal)"
		case 4:
			priority = "4 (Low)"
		case 5:
			priority = "5 (Lowest)"
		}
		m.SetHeader("X-Priority", priority)
	}

	// 设置邮件内容
	if config.IsHTML {
		m.SetBody("text/html", config.Body)
	} else {
		m.SetBody("text/plain", config.Body)
	}

	// 添加附件
	for _, attachment := range config.Attachments {
		if attachment != "" {
			m.Attach(attachment)
		}
	}

	// 使用EmailConfig的GetPort方法获取端口
	port := config.GetPort()

	// 创建SMTP拨号器
	d := gomail.NewDialer(config.SMTPHost, port, config.Username, config.Password)

	// 根据UseTLS配置设置TLS
	if config.UseTLS {
		// 使用STARTTLS（通常用于587端口）
		d.TLSConfig = &tls.Config{ServerName: config.SMTPHost}
	} else {
		// 使用SSL/TLS（通常用于465端口）
		d.SSL = true
	}

	// 发送邮件
	if err := d.DialAndSend(m); err != nil {
		log.Printf("邮件发送失败: %v", err)
		return fmt.Errorf("发送邮件失败: %v", err)
	}

	log.Printf("邮件发送成功: %s -> %v", config.Subject, config.To)
	return nil
}

// 发送钉钉消息（EmailSender不实现此方法）
func (e *EmailSender) SendDingTalk(ctx context.Context, config *types.DingTalkConfig) error {
	return fmt.Errorf("EmailSender不支持发送钉钉消息")
}
