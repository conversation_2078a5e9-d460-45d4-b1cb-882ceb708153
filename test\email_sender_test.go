package test

import (
	"context"
	"log"
	"testing"

	"taskReminder/sender"
	"taskReminder/types"
)

// 测试邮件发送器的新功能
func TestEmailSenderEnhanced(t *testing.T) {
	log.Println("=== 测试增强的邮件发送器功能 ===")

	// 创建邮件发送器
	emailSender := sender.NewEmailSender()
	ctx := context.Background()

	// 测试1：基本邮件发送
	log.Println("\n--- 测试1：基本邮件发送 ---")
	basicConfig := &types.EmailConfig{
		SMTPHost: "smtp.163.com",
		SMTPPort: 587,
		Username: "<EMAIL>",
		Password: "test_password",
		UseTLS:   true,
		From:     "<EMAIL>",
		To:       []string{"<EMAIL>"},
		Subject:  "基本邮件测试",
		Body:     "这是一封基本的测试邮件。",
		IsHTML:   false,
	}

	err := emailSender.SendEmail(ctx, basicConfig)
	if err != nil {
		log.Printf("基本邮件发送测试失败: %v", err)
	} else {
		log.Printf("基本邮件发送测试成功")
	}

	// 测试2：带抄送和密送的邮件
	log.Println("\n--- 测试2：带抄送和密送的邮件 ---")
	ccBccConfig := &types.EmailConfig{
		SMTPHost: "smtp.qq.com",
		SMTPPort: 465,
		Username: "<EMAIL>",
		Password: "test_password",
		UseTLS:   false, // 465端口使用SSL
		From:     "<EMAIL>",
		To:       []string{"<EMAIL>", "<EMAIL>"},
		CC:       []string{"<EMAIL>", "<EMAIL>"},
		BCC:      []string{"<EMAIL>"},
		Subject:  "抄送密送测试邮件",
		Body:     "这是一封包含抄送和密送的测试邮件。",
		IsHTML:   false,
	}

	err = emailSender.SendEmail(ctx, ccBccConfig)
	if err != nil {
		log.Printf("抄送密送邮件发送测试失败: %v", err)
	} else {
		log.Printf("抄送密送邮件发送测试成功")
	}

	// 测试3：HTML邮件带优先级
	log.Println("\n--- 测试3：HTML邮件带优先级 ---")
	htmlConfig := &types.EmailConfig{
		SMTPHost: "smtp.gmail.com",
		SMTPPort: 587,
		Username: "<EMAIL>",
		Password: "test_password",
		UseTLS:   true,
		From:     "<EMAIL>",
		To:       []string{"<EMAIL>"},
		Subject:  "高优先级HTML邮件",
		Body: `
			<html>
			<body>
				<h1>重要通知</h1>
				<p>这是一封<strong>高优先级</strong>的HTML邮件。</p>
				<ul>
					<li>项目1</li>
					<li>项目2</li>
					<li>项目3</li>
				</ul>
			</body>
			</html>
		`,
		IsHTML:   true,
		Priority: 1, // 最高优先级
	}

	err = emailSender.SendEmail(ctx, htmlConfig)
	if err != nil {
		log.Printf("HTML优先级邮件发送测试失败: %v", err)
	} else {
		log.Printf("HTML优先级邮件发送测试成功")
	}

	// 测试4：带附件的邮件
	log.Println("\n--- 测试4：带附件的邮件 ---")
	attachmentConfig := &types.EmailConfig{
		SMTPHost: "smtp.163.com",
		SMTPPort: 587,
		Username: "<EMAIL>",
		Password: "test_password",
		UseTLS:   true,
		From:     "<EMAIL>",
		To:       []string{"<EMAIL>"},
		Subject:  "带附件的邮件",
		Body:     "这是一封包含附件的邮件，请查收。",
		IsHTML:   false,
		Attachments: []string{
			"README.md",
			"go.mod",
		},
	}

	err = emailSender.SendEmail(ctx, attachmentConfig)
	if err != nil {
		log.Printf("附件邮件发送测试失败: %v", err)
	} else {
		log.Printf("附件邮件发送测试成功")
	}

	// 测试5：完整功能邮件
	log.Println("\n--- 测试5：完整功能邮件 ---")
	fullConfig := &types.EmailConfig{
		SMTPHost: "smtp.163.com",
		SMTPPort: 587,
		Username: "<EMAIL>",
		Password: "test_password",
		UseTLS:   true,
		From:     "<EMAIL>",
		To:       []string{"<EMAIL>", "<EMAIL>"},
		CC:       []string{"<EMAIL>"},
		BCC:      []string{"<EMAIL>"},
		Subject:  "完整功能测试邮件",
		Body: `
			<html>
			<body>
				<h2>完整功能测试</h2>
				<p>这封邮件包含了所有新功能：</p>
				<ul>
					<li>多个收件人</li>
					<li>抄送和密送</li>
					<li>HTML格式</li>
					<li>中等优先级</li>
					<li>附件</li>
				</ul>
				<p><em>感谢您的关注！</em></p>
			</body>
			</html>
		`,
		IsHTML:   true,
		Priority: 3, // 普通优先级
		Attachments: []string{
			"go.mod",
		},
	}

	err = emailSender.SendEmail(ctx, fullConfig)
	if err != nil {
		log.Printf("完整功能邮件发送测试失败: %v", err)
	} else {
		log.Printf("完整功能邮件发送测试成功")
	}
}

// 测试邮件配置验证
func TestEmailConfigValidation(t *testing.T) {
	log.Println("\n=== 测试邮件配置验证功能 ===")

	emailSender := sender.NewEmailSender()
	ctx := context.Background()

	// 测试1：空配置
	log.Println("\n--- 测试1：空配置验证 ---")
	err := emailSender.SendEmail(ctx, nil)
	if err != nil {
		log.Printf("空配置验证成功: %v", err)
	} else {
		log.Printf("空配置验证失败：应该返回错误")
	}

	// 测试2：缺少必要字段
	log.Println("\n--- 测试2：缺少必要字段验证 ---")
	invalidConfig := &types.EmailConfig{
		SMTPHost: "smtp.test.com",
		// 缺少Username, Password, From, To等必要字段
	}

	err = emailSender.SendEmail(ctx, invalidConfig)
	if err != nil {
		log.Printf("缺少必要字段验证成功: %v", err)
	} else {
		log.Printf("缺少必要字段验证失败：应该返回错误")
	}

	// 测试3：端口自动选择
	log.Println("\n--- 测试3：端口自动选择验证 ---")
	autoPortConfig := &types.EmailConfig{
		SMTPHost: "smtp.test.com",
		// SMTPPort: 0, // 不设置端口，让系统自动选择
		Username: "<EMAIL>",
		Password: "test_password",
		UseTLS:   true, // 应该自动选择587端口
		From:     "<EMAIL>",
		To:       []string{"<EMAIL>"},
		Subject:  "端口自动选择测试",
		Body:     "测试端口自动选择功能",
	}

	port := autoPortConfig.GetPort()
	expectedPort := 587 // UseTLS=true时应该是587
	if port == expectedPort {
		log.Printf("端口自动选择验证成功: 选择了端口 %d", port)
	} else {
		log.Printf("端口自动选择验证失败: 期望端口 %d，实际端口 %d", expectedPort, port)
	}

	// 测试SSL端口选择
	autoPortConfig.UseTLS = false // 应该自动选择465端口
	port = autoPortConfig.GetPort()
	expectedPort = 465
	if port == expectedPort {
		log.Printf("SSL端口自动选择验证成功: 选择了端口 %d", port)
	} else {
		log.Printf("SSL端口自动选择验证失败: 期望端口 %d，实际端口 %d", expectedPort, port)
	}
}

// 基准测试
func BenchmarkEmailSender(b *testing.B) {
	emailSender := sender.NewEmailSender()
	ctx := context.Background()

	config := &types.EmailConfig{
		SMTPHost: "smtp.test.com",
		SMTPPort: 587,
		Username: "<EMAIL>",
		Password: "test_password",
		UseTLS:   true,
		From:     "<EMAIL>",
		To:       []string{"<EMAIL>"},
		Subject:  "基准测试邮件",
		Body:     "基准测试内容",
		IsHTML:   false,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = emailSender.SendEmail(ctx, config)
	}
}
