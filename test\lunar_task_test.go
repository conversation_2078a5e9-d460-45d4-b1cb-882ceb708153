package test

import (
	"testing"

	"taskReminder/scheduler"
	"taskReminder/types"
)

// 测试农历cron表达式解析
func TestLunarCronParsing(t *testing.T) {
	s := scheduler.NewTaskScheduler()

	// 测试用例
	testCases := []struct {
		name        string
		lunarCron   string
		shouldError bool
		description string
	}{
		{
			name:        "正常农历表达式1",
			lunarCron:   "0 9 1 1",
			shouldError: false,
			description: "农历正月初一上午9点",
		},
		{
			name:        "正常农历表达式2",
			lunarCron:   "30 14 15 8",
			shouldError: false,
			description: "农历八月十五下午2点30分",
		},
		{
			name:        "通配符表达式",
			lunarCron:   "0 * * *",
			shouldError: false,
			description: "每小时整点",
		},
		{
			name:        "错误格式1",
			lunarCron:   "0 9 1",
			shouldError: true,
			description: "缺少农历月份",
		},
		{
			name:        "错误格式2",
			lunarCron:   "60 9 1 1",
			shouldError: true,
			description: "分钟超出范围",
		},
		{
			name:        "错误格式3",
			lunarCron:   "0 25 1 1",
			shouldError: true,
			description: "小时超出范围",
		},
		{
			name:        "错误格式4",
			lunarCron:   "0 9 32 1",
			shouldError: true,
			description: "农历日超出范围",
		},
		{
			name:        "错误格式5",
			lunarCron:   "0 9 1 13",
			shouldError: true,
			description: "农历月超出范围",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			config := &types.TaskConfig{
				Name: tc.name,
				Schedule: types.ScheduleConfig{
					Type:      types.LunarSchedule,
					LunarCron: tc.lunarCron,
				},
				MessageTypes: []types.MessageType{types.DingTalkMessage},
				DingTalk: &types.DingTalkConfig{
					WebhookURL: "https://test.webhook.url",
					Title:      "测试",
					Text:       "测试消息",
				},
				Enabled: true,
			}

			err := s.AddTask(tc.name, config)

			if tc.shouldError {
				if err == nil {
					t.Errorf("期望出现错误，但没有错误发生")
				}
			} else {
				if err != nil {
					t.Errorf("不期望出现错误，但发生了错误: %v", err)
				}
			}
		})
	}
}

// 测试Description字段的使用
func TestDescriptionFieldUsage(t *testing.T) {
	s := scheduler.NewTaskScheduler()

	// 添加带Description的任务
	config := &types.TaskConfig{
		Name: "测试任务",
		Schedule: types.ScheduleConfig{
			Type:      types.LunarSchedule,
			LunarCron: "0 9 1 1",
		},
		MessageTypes: []types.MessageType{types.DingTalkMessage},
		DingTalk: &types.DingTalkConfig{
			WebhookURL: "https://test.webhook.url",
			Title:      "测试",
			Text:       "测试消息",
		},
		Enabled: true,
	}

	err := s.AddTask("test_task", config)
	if err != nil {
		t.Fatalf("添加任务失败: %v", err)
	}

	// 测试获取任务状态
	status := s.GetTaskStatus()
	if len(status) != 1 {
		t.Errorf("期望1个任务，实际得到%d个", len(status))
	}

	expectedStatus := "测试任务 - 这是一个用于测试Description字段功能的任务"
	if status["test_task"] != expectedStatus {
		t.Errorf("期望状态: %s, 实际状态: %s", expectedStatus, status["test_task"])
	}

	// 测试没有Description的任务
	configNoDesc := &types.TaskConfig{
		Name: "无描述任务",
		Schedule: types.ScheduleConfig{
			Type:      types.LunarSchedule,
			LunarCron: "0 10 1 1",
		},
		MessageTypes: []types.MessageType{types.DingTalkMessage},
		DingTalk: &types.DingTalkConfig{
			WebhookURL: "https://test.webhook.url",
			Title:      "测试",
			Text:       "测试消息",
		},
		Enabled: true,
	}

	err = s.AddTask("test_task_no_desc", configNoDesc)
	if err != nil {
		t.Fatalf("添加任务失败: %v", err)
	}

	status = s.GetTaskStatus()
	if len(status) != 2 {
		t.Errorf("期望2个任务，实际得到%d个", len(status))
	}

	if status["test_task_no_desc"] != "无描述任务" {
		t.Errorf("期望状态: 无描述任务, 实际状态: %s", status["test_task_no_desc"])
	}
}
