package types

// 钉钉消息类型
type DingTalkMsgType string

const (
	DingTalkText     DingTalkMsgType = "text"     // 文本消息
	DingTalkMarkdown DingTalkMsgType = "markdown" // Markdown消息
)

// 钉钉配置
type DingTalkConfig struct {
	WebhookURL string          `json:"webhook_url"` // 钉钉机器人webhook地址
	Secret     string          `json:"secret"`      // 签名密钥（可选）
	AtMobiles  []string        `json:"at_mobiles"`  // @的手机号列表
	AtAll      bool            `json:"at_all"`      // 是否@所有人
	Title      string          `json:"title"`       // 消息标题
	Text       string          `json:"text"`        // 消息内容
	MsgType    DingTalkMsgType `json:"msg_type"`    // 消息类型，默认为markdown
}
