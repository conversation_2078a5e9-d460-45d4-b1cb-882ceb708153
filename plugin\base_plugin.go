package plugin

import (
	"context"
	"taskReminder/types"
)

// 插件静态信息结构 - 用于注册时的基本信息
type PluginStaticInfo struct {
	PluginID          string // 插件英文名称（唯一标识）
	PluginDescription string // 插件描述
}

// 插件调度配置结构 - 用于调度器的静态配置
type PluginScheduleConfig struct {
	Schedule       types.ScheduleConfig // 调度配置
	Enabled        bool                 // 是否启用
	RunImmediately bool                 // 是否立即执行（用于测试）
}

// 插件运行时内容结构 - 用于实际执行时的动态内容
type PluginRuntimeContent struct {
	Title           string                // 消息标题
	DingTalkContent string                // 钉钉消息内容
	EmailContent    string                // 邮件消息内容
	MessageTypes    []types.MessageType   // 要发送的消息类型
	EmailConfig     *types.EmailConfig    // 邮件配置（可选，用于覆盖默认配置）
	DingTalkConfig  *types.DingTalkConfig // 钉钉配置（可选，用于覆盖默认配置）
}

// 简化插件接口 - 分离静态信息和动态内容
type BasePlugin interface {
	// 获取插件静态信息（仅在注册时调用一次）
	GetStaticInfo() *PluginStaticInfo

	// 获取插件调度配置（仅在注册时调用一次）
	GetScheduleConfig() *PluginScheduleConfig

	// 生成运行时内容（每次执行时调用）
	GenerateContent(ctx context.Context) (*PluginRuntimeContent, error)
}

// 基础插件包装器 - 实现完整的Plugin接口
type BasePluginWrapper struct {
	basePlugin     BasePlugin
	staticInfo     *PluginStaticInfo     // 缓存静态信息
	scheduleConfig *PluginScheduleConfig // 缓存调度配置
}

// 创建基础插件包装器
func NewBasePlugin(basePlugin BasePlugin) *BasePluginWrapper {
	wrapper := &BasePluginWrapper{
		basePlugin: basePlugin,
	}

	// 在创建时获取并缓存静态信息，避免重复调用
	wrapper.staticInfo = basePlugin.GetStaticInfo()
	wrapper.scheduleConfig = basePlugin.GetScheduleConfig()

	return wrapper
}

// 实现Plugin接口的GetName方法
func (b *BasePluginWrapper) GetName() string {
	if b.staticInfo != nil {
		return b.staticInfo.PluginID
	}
	return "unknown"
}

// 实现Plugin接口的GetDescription方法
func (b *BasePluginWrapper) GetDescription() string {
	if b.staticInfo != nil {
		return b.staticInfo.PluginDescription
	}
	return "未知插件"
}

// 实现Plugin接口的GetTaskConfig方法
func (b *BasePluginWrapper) GetTaskConfig() *types.TaskConfig {
	if b.scheduleConfig == nil {
		return &types.TaskConfig{
			Name:         "未配置",
			MessageTypes: []types.MessageType{},
			Enabled:      false,
		}
	}

	// 构建基础任务配置（仅用于调度，不包含具体内容）
	return &types.TaskConfig{
		Name:           b.staticInfo.PluginID,
		Schedule:       b.scheduleConfig.Schedule,
		MessageTypes:   []types.MessageType{}, // 静态配置不包含消息类型，运行时确定
		Enabled:        b.scheduleConfig.Enabled,
		RunImmediately: b.scheduleConfig.RunImmediately,
	}
}

// 实现Plugin接口的GetRuntimeConfig方法
func (b *BasePluginWrapper) GetRuntimeConfig(ctx context.Context) *types.TaskConfig {
	// 生成运行时内容
	content, err := b.basePlugin.GenerateContent(ctx)
	if err != nil {
		// 如果生成内容失败，返回空配置（不发送消息）
		return &types.TaskConfig{
			Name:         b.staticInfo.PluginID,
			MessageTypes: []types.MessageType{},
			Enabled:      false,
		}
	}

	// 如果没有内容，返回空配置
	if content == nil {
		return &types.TaskConfig{
			Name:         b.staticInfo.PluginID,
			MessageTypes: []types.MessageType{},
			Enabled:      false,
		}
	}

	// 构建运行时配置
	runtimeConfig := &types.TaskConfig{
		Name:         b.staticInfo.PluginID,
		Schedule:     b.scheduleConfig.Schedule,
		MessageTypes: content.MessageTypes,
		Enabled:      len(content.MessageTypes) > 0, // 有消息类型才启用
	}

	// 设置邮件配置
	if content.EmailConfig != nil {
		runtimeConfig.Email = content.EmailConfig
	} else if len(content.EmailContent) > 0 {
		// 使用默认邮件配置 + 动态内容
		runtimeConfig.Email = &types.EmailConfig{
			From:     SinaEmailConfig.From,
			To:       []string{"<EMAIL>"}, // 默认收件人
			SMTPHost: SinaEmailConfig.SMTPHost,
			SMTPPort: SinaEmailConfig.SMTPPort,
			Username: SinaEmailConfig.Username,
			Password: SinaEmailConfig.Password,
			FromName: SinaEmailConfig.FromName,
			UseTLS:   false, // Sina邮箱使用SSL
			Subject:  content.Title,
			Body:     content.EmailContent,
			IsHTML:   true,
		}
	}

	// 设置钉钉配置
	if content.DingTalkConfig != nil {
		runtimeConfig.DingTalk = content.DingTalkConfig
	} else if len(content.DingTalkContent) > 0 {
		// 使用默认钉钉配置 + 动态内容
		runtimeConfig.DingTalk = &types.DingTalkConfig{
			WebhookURL: DingTalkConfig.WebhookURL,
			Secret:     DingTalkConfig.Secret,
			AtMobiles:  []string{},
			AtAll:      false,
			Title:      content.Title,
			Text:       content.DingTalkContent,
			MsgType:    types.DingTalkMarkdown,
		}
	}

	return runtimeConfig
}

// 实现Plugin接口的OnExecute方法
func (b *BasePluginWrapper) OnExecute(ctx context.Context) error {
	// 基础插件包装器不需要特殊的执行逻辑
	return nil
}
