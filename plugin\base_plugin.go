package plugin

import (
	"context"
	"taskReminder/types"
)

// 简化插件配置结构
type BasePluginConfig struct {
	// 基本信息
	PluginID          string // 插件英文名称（唯一标识）
	PluginDescription string // 插件描述

	// 完整的任务配置 - 包含所有发送方式的配置
	TaskConfig *types.TaskConfig // 完整任务配置，包含所有信息
}

// 简化插件接口 - 用户只需要实现一个方法
type BasePlugin interface {
	// 获取插件配置（每次调用都会重新生成，支持动态内容）
	// 用户通过设置MessageTypes来控制是否发送消息
	GetPluginConfig(ctx context.Context) *BasePluginConfig
}

// 基础插件包装器 - 实现完整的Plugin接口
type BasePluginWrapper struct {
	basePlugin BasePlugin
}

// 创建基础插件包装器
func NewBasePlugin(basePlugin BasePlugin) *BasePluginWrapper {
	return &BasePluginWrapper{
		basePlugin: basePlugin,
	}
}

// 实现Plugin接口的GetName方法
func (b *BasePluginWrapper) GetName() string {
	// 使用空上下文获取基本配置信息
	config := b.basePlugin.GetPluginConfig(context.Background())
	if config != nil {
		return config.PluginID
	}
	return "unknown"
}

// 实现Plugin接口的GetDescription方法
func (b *BasePluginWrapper) GetDescription() string {
	// 使用空上下文获取基本配置信息
	config := b.basePlugin.GetPluginConfig(context.Background())
	if config != nil {
		return config.PluginDescription
	}
	return "未知插件"
}

// 实现Plugin接口的GetTaskConfig方法
func (b *BasePluginWrapper) GetTaskConfig() *types.TaskConfig {
	// 使用空上下文获取基本配置信息
	config := b.basePlugin.GetPluginConfig(context.Background())
	if config != nil && config.TaskConfig != nil {
		// 复制用户配置，避免修改原始配置
		taskConfig := *config.TaskConfig
		return &taskConfig
	}

	// 如果用户没有提供TaskConfig，返回空配置
	return &types.TaskConfig{
		Name:         "未配置",
		MessageTypes: []types.MessageType{},
		Enabled:      false,
	}
}

// 实现Plugin接口的GetRuntimeConfig方法
func (b *BasePluginWrapper) GetRuntimeConfig(ctx context.Context) *types.TaskConfig {
	// 重新调用GetPluginConfig获取实时内容
	runtimePluginConfig := b.basePlugin.GetPluginConfig(ctx)

	// 复制配置避免修改原始配置
	runtimeConfig := *runtimePluginConfig.TaskConfig
	return &runtimeConfig
}

// 实现Plugin接口的OnExecute方法
func (b *BasePluginWrapper) OnExecute(ctx context.Context) error {
	// 基础插件包装器不需要特殊的执行逻辑
	return nil
}
