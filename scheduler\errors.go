package scheduler

import "errors"

// 调度器相关错误
var (
	ErrTaskConfigNil            = errors.New("任务配置不能为空")
	ErrUnsupportedScheduleType  = errors.New("不支持的调度类型")
	ErrSchedulerAlreadyRunning  = errors.New("调度器已经在运行")
	ErrSchedulerNotRunning      = errors.New("调度器未运行")
	ErrCronExpressionEmpty      = errors.New("cron表达式不能为空")
	ErrIntervalInvalid          = errors.New("间隔时间必须大于0")
	ErrLunarCronEmpty           = errors.New("农历cron表达式不能为空")
	ErrUnsupportedTimeUnit      = errors.New("不支持的时间单位")
)
