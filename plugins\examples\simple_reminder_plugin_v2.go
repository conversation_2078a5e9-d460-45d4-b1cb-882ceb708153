package examples

import (
	"context"
	"fmt"
	"time"

	"taskReminder/plugin"
	"taskReminder/types"
)

// SimpleReminderPluginV2 简单提醒插件 - 重构版本
// 展示新插件架构的使用方式：分离静态信息和动态内容生成
type SimpleReminderPluginV2 struct{}

// GetStaticInfo 获取插件静态信息（仅在注册时调用一次）
// 这里只包含插件的基本标识信息，不涉及任何动态内容生成
func (p *SimpleReminderPluginV2) GetStaticInfo() *plugin.PluginStaticInfo {
	return &plugin.PluginStaticInfo{
		PluginID:          "simple_reminder_v2",
		PluginDescription: "简单定时提醒插件 - 重构版本（延迟执行）",
	}
}

// GetScheduleConfig 获取插件调度配置（仅在注册时调用一次）
// 这里只包含调度相关的静态配置，不涉及消息内容
func (p *SimpleReminderPluginV2) GetScheduleConfig() *plugin.PluginScheduleConfig {
	return &plugin.PluginScheduleConfig{
		Schedule: types.ScheduleConfig{
			Type:     types.CronSchedule,
			CronExpr: "0 */2 * * * *", // 每2分钟执行一次
		},
		Enabled:        true,
		RunImmediately: true, // 设置为true可以立即测试
	}
}

// GenerateContent 生成运行时内容（每次执行时调用）
// 这里才是真正生成消息内容的地方，在实际需要发送消息时才会被调用
func (p *SimpleReminderPluginV2) GenerateContent(ctx context.Context) (*plugin.PluginRuntimeContent, error) {
	// 实时生成消息内容
	currentTime := time.Now().Format("2006-01-02 15:04:05")
	title := "⏰ 定时提醒"

	// 生成钉钉消息内容
	dingTalkContent := fmt.Sprintf(`### 📅 定时提醒消息

**提醒时间**: %s

**消息内容**:
- 🔔 这是一条定时提醒消息
- ⏰ 每2分钟发送一次
- 🤖 由系统自动生成

**系统状态**:
- ✅ 系统运行正常
- 📊 消息发送成功
- 🔄 下次提醒: 2分钟后

---
*💡 这是重构版本的插件，内容在执行时才生成*`, currentTime)

	// 生成邮件内容
	emailContent := fmt.Sprintf(`
<h2>📅 定时提醒消息</h2>
<p><strong>提醒时间</strong>: %s</p>

<h3>📋 消息内容</h3>
<ul>
<li>🔔 这是一条定时提醒消息</li>
<li>⏰ 每2分钟发送一次</li>
<li>🤖 由系统自动生成</li>
</ul>

<h3>📊 系统状态</h3>
<ul>
<li>✅ 系统运行正常</li>
<li>📊 消息发送成功</li>
<li>🔄 下次提醒: 2分钟后</li>
</ul>

<hr>
<p><em>💡 这是重构版本的插件，内容在执行时才生成</em></p>
`, currentTime)

	// 根据当前时间决定是否发送消息
	// 例如：只在工作时间发送消息
	now := time.Now()
	hour := now.Hour()
	
	// 只在9点到18点之间发送消息
	if hour >= 9 && hour <= 18 {
		return &plugin.PluginRuntimeContent{
			Title:           title,
			DingTalkContent: dingTalkContent,
			EmailContent:    emailContent,
			MessageTypes:    []types.MessageType{types.DingTalkMessage, types.EmailMessage},
		}, nil
	} else {
		// 非工作时间，不发送消息
		return &plugin.PluginRuntimeContent{
			Title:        title,
			MessageTypes: []types.MessageType{}, // 空的消息类型列表表示不发送消息
		}, nil
	}
}

// WeatherReminderPluginV2 天气提醒插件 - 展示条件性消息发送
type WeatherReminderPluginV2 struct{}

func (p *WeatherReminderPluginV2) GetStaticInfo() *plugin.PluginStaticInfo {
	return &plugin.PluginStaticInfo{
		PluginID:          "weather_reminder_v2",
		PluginDescription: "天气提醒插件 - 重构版本（条件性发送）",
	}
}

func (p *WeatherReminderPluginV2) GetScheduleConfig() *plugin.PluginScheduleConfig {
	return &plugin.PluginScheduleConfig{
		Schedule: types.ScheduleConfig{
			Type:     types.CronSchedule,
			CronExpr: "0 0 7 * * *", // 每天早上7点执行
		},
		Enabled:        true,
		RunImmediately: false,
	}
}

func (p *WeatherReminderPluginV2) GenerateContent(ctx context.Context) (*plugin.PluginRuntimeContent, error) {
	// 模拟获取天气信息
	weather := p.getWeatherInfo()
	
	// 只有在特殊天气情况下才发送提醒
	if weather.NeedAlert {
		title := fmt.Sprintf("🌤️ 天气提醒 - %s", weather.Condition)
		
		dingTalkContent := fmt.Sprintf(`### 🌤️ 天气提醒

**当前天气**: %s
**温度**: %s
**提醒**: %s

---
*🤖 天气监控服务*`, weather.Condition, weather.Temperature, weather.Alert)

		emailContent := fmt.Sprintf(`
<h2>🌤️ 天气提醒</h2>
<p><strong>当前天气</strong>: %s</p>
<p><strong>温度</strong>: %s</p>
<p><strong>提醒</strong>: %s</p>
<hr>
<p><em>🤖 天气监控服务</em></p>
`, weather.Condition, weather.Temperature, weather.Alert)

		return &plugin.PluginRuntimeContent{
			Title:           title,
			DingTalkContent: dingTalkContent,
			EmailContent:    emailContent,
			MessageTypes:    []types.MessageType{types.DingTalkMessage},
		}, nil
	}
	
	// 天气正常，不发送消息
	return &plugin.PluginRuntimeContent{
		MessageTypes: []types.MessageType{},
	}, nil
}

// WeatherInfo 天气信息结构
type WeatherInfo struct {
	Condition   string
	Temperature string
	Alert       string
	NeedAlert   bool
}

// getWeatherInfo 模拟获取天气信息
func (p *WeatherReminderPluginV2) getWeatherInfo() WeatherInfo {
	// 模拟天气数据
	// 在实际应用中，这里会调用天气API
	now := time.Now()
	
	// 模拟：周末不发送提醒
	if now.Weekday() == time.Saturday || now.Weekday() == time.Sunday {
		return WeatherInfo{
			Condition:   "晴天",
			Temperature: "22°C",
			Alert:       "",
			NeedAlert:   false,
		}
	}
	
	// 模拟：工作日有概率发送天气提醒
	if now.Hour() == 7 && now.Minute() < 30 {
		return WeatherInfo{
			Condition:   "雨天",
			Temperature: "18°C",
			Alert:       "今日有雨，记得带伞！",
			NeedAlert:   true,
		}
	}
	
	return WeatherInfo{
		Condition:   "多云",
		Temperature: "25°C",
		Alert:       "",
		NeedAlert:   false,
	}
}
