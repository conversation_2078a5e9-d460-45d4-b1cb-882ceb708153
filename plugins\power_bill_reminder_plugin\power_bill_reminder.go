package power_bill_reminder_plugin

import (
	"context"
	"fmt"
	"taskReminder/plugin"
	"taskReminder/types"
	"time"
)

// 电费缴费提醒插件
type PowerBillReminderPlugin struct{}

func (p *PowerBillReminderPlugin) GetPluginConfig(ctx context.Context) *plugin.BasePluginConfig {
	title, dingTalkContent, emailContent := generateTitleAndContent()

	// 根据content是否为空决定MessageTypes
	var messageTypes []types.MessageType
	if dingTalkContent != "" {
		messageTypes = append(messageTypes, types.DingTalkMessage)
	}
	if emailContent != "" {
		messageTypes = append(messageTypes, types.EmailMessage)
	}

	return &plugin.BasePluginConfig{
		// 基本信息
		PluginID:          "power_bill_reminder",
		PluginDescription: "每月电费缴费提醒 - 每月6日9点和10点提醒，享受缴费优惠",

		// 完整的任务配置 - 包含动态生成的消息内容
		TaskConfig: &types.TaskConfig{
			Name: "电费缴费提醒",
			Schedule: types.ScheduleConfig{
				Type:     types.CronSchedule,
				CronExpr: "0 0 9,10 6 * *", // 每月6日9点和10点各提醒一次
			},
			// RunImmediately: true, // 设置为立即执行，用于测试
			MessageTypes: messageTypes,
			// 邮件配置 - 使用HTML格式
			Email: &types.EmailConfig{
				SMTPHost: plugin.SinaEmailConfig.SMTPHost,
				SMTPPort: plugin.SinaEmailConfig.SMTPPort,
				Username: plugin.SinaEmailConfig.Username,
				Password: plugin.SinaEmailConfig.Password,
				From:     plugin.SinaEmailConfig.From,
				FromName: plugin.SinaEmailConfig.FromName,
				To:       []string{"<EMAIL>", "<EMAIL>"}, // 收件人
				Subject:  title,                                          // 邮件标题
				Body:     emailContent,                                   // 邮件内容
				IsHTML:   true,                                           // 使用HTML格式
			},
			// 钉钉配置 - 用户可以自定义markdown或文本格式
			DingTalk: &types.DingTalkConfig{
				WebhookURL: plugin.DingTalkConfig.WebhookURL,
				Secret:     plugin.DingTalkConfig.Secret,
				AtMobiles:  []string{},
				AtAll:      false,
				Title:      title,                  // 钉钉标题
				Text:       dingTalkContent,        // 钉钉内容
				MsgType:    types.DingTalkMarkdown, // 可以设置为Markdown使用纯文本格式
			},
			Enabled: true,
		},
	}
}

func generateTitleAndContent() (title, dingTalkContent, emailContent string) {
	// 实时生成消息内容
	now := time.Now()
	currentTime := now.Format("2006-01-02 15:04:05")
	currentMonth := now.Format("2006年01月")
	currentHour := now.Hour()

	// 根据时间生成不同的提醒标题
	var timeDesc string
	if currentHour == 9 {
		timeDesc = "首次提醒"
	} else {
		timeDesc = "再次提醒"
	}

	title = fmt.Sprintf("💡 %s电费缴费提醒 - %s", currentMonth, timeDesc)

	// 钉钉消息内容 - 使用Markdown格式
	dingTalkContent = fmt.Sprintf(`> **🎉 今日缴费享优惠！**

**提醒时间**: %s

**重要提醒**：
- 📅 今天是每月6日，缴费享受优惠政策
- 💰 建议今日完成电费缴费，享受折扣优惠
- ⏰ 避免逾期产生滞纳金

**缴费方式**：
- 💳 微信：生活缴费 → 电费

---
*💡 温馨提示：每月6日缴费可享受优惠，请及时缴费*`, currentTime)

	// HTML邮件内容
	emailContent = fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<style>
		body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
		.header { background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%); color: white; padding: 20px; text-align: center; }
		.content { padding: 20px; }
		.highlight { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }
		.methods { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
		.footer { background-color: #6c757d; color: white; padding: 10px; text-align: center; font-size: 12px; }
		ul { padding-left: 20px; }
		li { margin: 8px 0; }
	</style>
</head>
<body>	
	<div class="content">
		<div class="highlight">
			<h3>🎉 今日缴费享优惠！</h3>
			<p><strong>提醒时间</strong>：%s</p>
			<p><strong>重要提醒</strong>：今天是每月6日，缴费享受优惠政策，建议今日完成电费缴费！</p>
		</div>
		
		<h3>📋 缴费注意事项</h3>
		<ul>
			<li>💰 每月6日缴费可享受优惠折扣</li>
			<li>⏰ 避免逾期产生滞纳金</li>
			<li>📊 建议查看上月用电量，合理安排用电</li>
		</ul>
		
		<div class="methods">
			<h3>💳 推荐缴费方式</h3>
			<ul>
				<li>💳 <strong>微信</strong>：生活缴费 → 电费</li>
			</ul>
		</div>
	</div>
	
	<div class="footer">
		💡 温馨提示：每月6日缴费可享受优惠，请及时缴费 | 系统自动发送，请勿回复
	</div>
</body>
</html>`, currentTime)

	return title, dingTalkContent, emailContent
}
