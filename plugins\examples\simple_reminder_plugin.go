package examples

import (
	"context"
	"fmt"
	"time"

	"taskReminder/plugin"
	"taskReminder/types"
)

// 简单提醒插件示例
type SimpleReminderPlugin struct{}

// 获取插件配置（每次调用都会重新生成，支持动态内容）
func (p *SimpleReminderPlugin) GetPluginConfig(ctx context.Context) *plugin.BasePluginConfig {
	title, dingTalkContent, emailContent := generateTitleAndContent()

	// 根据content是否为空决定MessageTypes
	var messageTypes []types.MessageType
	if dingTalkContent != "" {
		messageTypes = append(messageTypes, types.DingTalkMessage)
	}
	if emailContent != "" {
		messageTypes = append(messageTypes, types.EmailMessage)
	}

	return &plugin.BasePluginConfig{
		// 基本信息
		PluginID:          "simple_reminder",
		PluginDescription: "简单定时提醒 - 每分钟发送一次提醒",

		// 完整的任务配置 - 包含动态生成的消息内容
		TaskConfig: &types.TaskConfig{
			Name: "简单定时提醒",
			Schedule: types.ScheduleConfig{
				Type:     types.CronSchedule,
				CronExpr: "0 */1 * * * *", // 每分钟执行一次
			},
			// RunImmediately: true, // 设置为立即执行，用于测试
			MessageTypes: messageTypes,
			// 邮件配置 - 用户可以自定义HTML或纯文本格式
			Email: &types.EmailConfig{
				SMTPHost: plugin.SinaEmailConfig.SMTPHost,
				SMTPPort: plugin.SinaEmailConfig.SMTPPort,
				Username: plugin.SinaEmailConfig.Username,
				Password: plugin.SinaEmailConfig.Password,
				From:     plugin.SinaEmailConfig.From,
				FromName: plugin.SinaEmailConfig.FromName,
				To:       []string{"<EMAIL>"}, // 收件人
				Subject:  title,                     // 邮件标题
				Body:     emailContent,              // 邮件内容
				IsHTML:   false,                     // 可以设置为true使用HTML格式
			},
			// 钉钉配置 - 用户可以自定义markdown或文本格式
			DingTalk: &types.DingTalkConfig{
				WebhookURL: plugin.DingTalkConfig.WebhookURL,
				Secret:     plugin.DingTalkConfig.Secret,
				AtMobiles:  []string{},
				AtAll:      false,
				Title:      title,                  // 钉钉标题
				Text:       dingTalkContent,        // 钉钉内容
				MsgType:    types.DingTalkMarkdown, // 可以设置为Markdown使用纯文本格式
			},
			Enabled: true,
		},
	}

}

func generateTitleAndContent() (title, dingTalkContent, emailContent string) {
	// 实时生成消息内容
	currentTime := time.Now().Format("2006-01-02 15:04:05")
	title = "⏰ 定时提醒"

	dingTalkContent = fmt.Sprintf(`### 📅 定时提醒消息

**当前时间**: %s

这是一条每分钟发送的定时提醒消息。

---
*系统自动发送，请勿回复*`, currentTime)

	// html邮件内容 emailContent
	emailContent = fmt.Sprintf(`<!DOCTYPE html>
	<html>
	<head>
		<meta charset="utf-8">
		<title>%s</title>
	</head>
	<body>
		<h1>%s</h1>
		<p>当前时间: %s</p>
		<p>这是一条每分钟发送的定时提醒消息。</p>
	</body>
	</html>`, title, title, currentTime)

	return title, dingTalkContent, emailContent
}
