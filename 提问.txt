我想写一个系统的需求说明书，请帮我完善需求说明书,暂时不需要你编码，大致需求如下
```
使用golang写一个定时消息提醒系统，技术架构，golang，
1、代码中包含发送邮件和发送钉钉
2、定时任务要支持多种类型的比如cron 表达式、每隔多少秒/分/时/周/天/月/年（这个需要一个开始时间参数）、农历的cron表达式等
3、添加多个定时消息任务就像调用插件一样简单， 把每种类型的定时任务都写一个插件示例并加上注释，调用插件时候要简单，提供相应参数（定时参数、邮件或者钉钉参数，发送消息内容等）然后选择发送类型（邮件或者钉钉，支持多个同时发送）即可
4、不要配置文件，直接在main中决定加载哪些插件，用户只要写插件即可，所有配置在插件中写
5、代码简洁规范，不要设计过于复杂的代码结构、使用中文注释，
```

很好，请优化一下，
1、scheduler分多个文件处理不同类型的任务
2、执行任务时控制台使用log库打印日志
3、优化一下测试用例，不要在测试用来中写main


smtp.sina.com
465
<EMAIL>
18dfe63a7516fa9f
<EMAIL>

https://oapi.dingtalk.com/robot/send?access_token=7debd389741b51484bf8e10726e397269e9896139496e9cba0f11f06b0cab423
SECdfe010745044c45132014d2f13476cf29792396b572429e73901cd4572c3a63d

https://oapi.dingtalk.com/robot/send?access_token=97b9ecd5646a172883880fee73288f36e4ada7db87a5ee5138041c5abab71d1e



// 复制邮件配置并设置动态内容
	emailConfig := *plugins.EmailConfig
	emailConfig.To = []string{"<EMAIL>"}
	emailConfig.Subject = "定时表达式提醒"
	emailConfig.Body = "定时提醒 - 每隔2分钟提醒一次，本次提醒时间：" + currentTime
	baseConfig.Email = &emailConfig

	// 复制钉钉配置并设置动态内容
	dingtalkConfig := *plugins.DingTalkConfig
	dingtalkConfig.Title = "定时表达式提醒"
	dingtalkConfig.Text = "定时提醒 - 每隔2分钟提醒一次，本次提醒时间：" + currentTime
	dingtalkConfig.MsgType = types.DingTalkMarkdown
	baseConfig.DingTalk = &dingtalkConfig


func (p *CrontabReminderPlugin) GetRuntimeConfig(ctx context.Context) *types.TaskConfig {
    currentTime := time.Now().Format("2006-01-02 15:04:05")
    baseConfig := *p.GetTaskConfig()
    baseConfig.Email = plugins.EmailConfig
    baseConfig.Email.To = []string{"<EMAIL>"}
    baseConfig.Email.Subject = "定时表达式提醒"
    baseConfig.Email.Body = "定时提醒 - 每隔2分钟提醒一次，本次提醒时间：" + currentTime

return &baseConfig
}
会改变全局变量plugins.EmailConfig的值吗
