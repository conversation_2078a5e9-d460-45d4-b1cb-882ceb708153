package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"taskReminder/plugin"
	"taskReminder/plugins/birthdays_plugin"
	"taskReminder/plugins/digitalplat_renewal_plugin"
	"taskReminder/plugins/glados_checkin_plugin"
	"taskReminder/plugins/power_bill_reminder_plugin"
)

func main() {
	log.Println("🚀 启动定时消息提醒系统...")

	// 创建插件管理器
	manager := plugin.NewManager()

	// 优雅的插件注册 - 一行代码搞定！
	manager.RegisterPlugins(
		// // 简化插件示例（注释掉）
		// plugins.NewBasePlugin(&examples.SimpleReminderPlugin{}), // 简化的定时提醒插件

		// 使用新的简化插件架构 - 生日提醒插件
		plugin.NewBasePlugin(&birthdays_plugin.BirthdayReminderPlugin{}), // 生日提醒（支持农历和阳历）

		// Digitalplat域名续期提醒插件
		plugin.NewBasePlugin(&digitalplat_renewal_plugin.DigitalplatRenewalPlugin{}), // Digitalplat免费域名续期提醒（每180天）

		// 每月6日电费缴费提醒
		plugin.NewBasePlugin(&power_bill_reminder_plugin.PowerBillReminderPlugin{}),

		// GLaDOS签到插件
		plugin.NewBasePlugin(&glados_checkin_plugin.GladosCheckinPlugin{}),
	)

	// 显示已注册的插件
	fmt.Println("\n📋 插件注册完成:")
	manager.ListPlugins()

	// 创建上下文，用于优雅关闭
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动插件管理器
	if err := manager.Start(ctx); err != nil {
		log.Printf("❌ 启动插件管理器失败: %v", err)
		os.Exit(1)
	}

	// 监听系统信号，实现优雅关闭
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	fmt.Println("\n✅ 系统启动成功！按 Ctrl+C 退出...")

	// 等待退出信号
	<-sigChan
	log.Println("\n🛑 收到退出信号，正在关闭系统...")

	// 停止插件管理器
	if err := manager.Stop(); err != nil {
		log.Printf("❌ 停止插件管理器失败: %v", err)
	}

	log.Println("👋 系统已安全退出，再见！")
}
