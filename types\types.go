package types

import (
	"context"
	"time"
)

// 定时任务类型枚举
type ScheduleType int

const (
	CronSchedule     ScheduleType = iota // cron表达式
	IntervalSchedule                     // 间隔时间
	LunarSchedule                        // 农历cron表达式
)

// 时间间隔单位
type IntervalUnit string

const (
	Second IntervalUnit = "second"
	Minute IntervalUnit = "minute"
	Hour   IntervalUnit = "hour"
	Day    IntervalUnit = "day"
	Week   IntervalUnit = "week"
	Month  IntervalUnit = "month"
	Year   IntervalUnit = "year"
)

// 消息发送类型
type MessageType int

const (
	EmailMessage    MessageType = iota // 邮件
	DingTalkMessage                    // 钉钉
)

// 定时配置
type ScheduleConfig struct {
	Type      ScheduleType `json:"type"`       // 定时类型
	CronExpr  string       `json:"cron_expr"`  // cron表达式
	Interval  int          `json:"interval"`   // 间隔数值
	Unit      IntervalUnit `json:"unit"`       // 间隔单位
	StartTime *time.Time   `json:"start_time"` // 开始时间（间隔类型需要）
	LunarCron string       `json:"lunar_cron"` // 农历cron表达式
}

// 任务配置
type TaskConfig struct {
	Name           string          `json:"name"`            // 任务名称
	Schedule       ScheduleConfig  `json:"schedule"`        // 定时配置
	MessageTypes   []MessageType   `json:"message_types"`   // 消息发送类型列表
	Email          *EmailConfig    `json:"email"`           // 邮件配置
	DingTalk       *DingTalkConfig `json:"dingtalk"`        // 钉钉配置
	Enabled        bool            `json:"enabled"`         // 是否启用
	RunImmediately bool            `json:"run_immediately"` // 是否立即执行（用于测试插件）
}

// 消息发送接口
type MessageSender interface {
	SendEmail(ctx context.Context, config *EmailConfig) error
	SendDingTalk(ctx context.Context, config *DingTalkConfig) error
}

// 定时任务调度器接口
type Scheduler interface {
	AddTask(taskID string, config *TaskConfig) error
	RemoveTask(taskID string) error
	Start(ctx context.Context) error
	Stop() error
}

// 插件接口
type Plugin interface {
	GetName() string                                  // 获取插件名称
	GetDescription() string                           // 获取插件描述
	GetTaskConfig() *TaskConfig                       // 获取任务配置（静态配置，用于调度）
	GetRuntimeConfig(ctx context.Context) *TaskConfig // 获取运行时配置（动态配置，用于执行）
	OnExecute(ctx context.Context) error              // 执行回调（可选，用于自定义逻辑）
}
