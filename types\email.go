package types

import "fmt"

// 邮件配置结构体
type EmailConfig struct {
	// SMTP服务器配置
	SMTPHost string `json:"smtp_host" yaml:"smtp_host"`
	SMTPPort int    `json:"smtp_port" yaml:"smtp_port"`
	Username string `json:"username" yaml:"username"`
	Password string `json:"password" yaml:"password"`
	UseTLS   bool   `json:"use_tls" yaml:"use_tls"`

	// 邮件头信息
	From        string   `json:"from" yaml:"from"`
	FromName    string   `json:"from_name,omitempty" yaml:"from_name,omitempty"` // 发件人显示名称
	To          []string `json:"to" yaml:"to"`
	CC          []string `json:"cc,omitempty" yaml:"cc,omitempty"`
	BCC         []string `json:"bcc,omitempty" yaml:"bcc,omitempty"`
	Subject     string   `json:"subject" yaml:"subject"`
	ReplyTo     string   `json:"reply_to,omitempty" yaml:"reply_to,omitempty"`           // 回复地址
	ReplyToName string   `json:"reply_to_name,omitempty" yaml:"reply_to_name,omitempty"` // 回复地址显示名称

	// 邮件内容
	Body   string `json:"body" yaml:"body"`
	IsHTML bool   `json:"is_html" yaml:"is_html"`

	// 附件
	Attachments []string `json:"attachments,omitempty" yaml:"attachments,omitempty"`

	// 其他配置
	Priority int `json:"priority,omitempty" yaml:"priority,omitempty"` // 1-5，1为最高优先级
}

// 验证邮件配置
func (c *EmailConfig) Validate() error {
	if c.SMTPHost == "" {
		return fmt.Errorf("SMTP服务器地址不能为空")
	}
	if c.Username == "" {
		return fmt.Errorf("用户名不能为空")
	}
	if c.Password == "" {
		return fmt.Errorf("密码不能为空")
	}
	if c.From == "" {
		return fmt.Errorf("发件人不能为空")
	}
	if len(c.To) == 0 {
		return fmt.Errorf("收件人不能为空")
	}
	if len(c.Body) == 0 {
		return fmt.Errorf("邮件内容不能为空")
	}
	return nil
}

// 获取默认端口
func (c *EmailConfig) GetPort() int {
	if c.SMTPPort == 0 {
		if c.UseTLS {
			return 587 // STARTTLS
		}
		return 465 // SSL
	}
	return c.SMTPPort
}
