package digitalplat_renewal_plugin

import (
	"context"
	"fmt"
	"time"

	"taskReminder/plugin"
	"taskReminder/types"
)

// Digitalplat域名续期提醒插件
type DigitalplatRenewalPlugin struct{}

// 获取插件配置
func (p *DigitalplatRenewalPlugin) GetPluginConfig(ctx context.Context) *plugin.BasePluginConfig {
	title, dingTalkContent, emailContent := p.generateTitleAndContent()

	// 根据content是否为空决定MessageTypes
	var messageTypes []types.MessageType
	if dingTalkContent != "" {
		messageTypes = append(messageTypes, types.DingTalkMessage)
	}
	if emailContent != "" {
		messageTypes = append(messageTypes, types.EmailMessage)
	}

	// 设置开始时间为2025年7月8日（使用本地时区）
	startTime, _ := time.ParseInLocation("2006-01-02 15:04:05", "2025-07-08 08:08:00", time.Local)

	return &plugin.BasePluginConfig{
		// 基本信息
		PluginID:          "digitalplat_renewal",
		PluginDescription: "Digitalplat免费域名续期提醒 - 每隔180天提醒一次",

		// 完整的任务配置
		TaskConfig: &types.TaskConfig{
			Name: "Digitalplat域名续期提醒",
			Schedule: types.ScheduleConfig{
				Type:      types.IntervalSchedule, // 使用间隔调度
				Interval:  180,                    // 180天
				Unit:      types.Day,              // 单位：天
				StartTime: &startTime,             // 开始时间：2025年7月8日
			},
			MessageTypes: messageTypes,
			// 邮件配置
			Email: &types.EmailConfig{
				SMTPHost: plugin.SinaEmailConfig.SMTPHost,
				SMTPPort: plugin.SinaEmailConfig.SMTPPort,
				Username: plugin.SinaEmailConfig.Username,
				Password: plugin.SinaEmailConfig.Password,
				From:     plugin.SinaEmailConfig.From,
				FromName: plugin.SinaEmailConfig.FromName,
				To:       []string{"<EMAIL>"}, // 收件人
				Subject:  title,                     // 邮件标题
				Body:     emailContent,              // 邮件内容
				IsHTML:   true,                      // 使用HTML格式
			},
			// 钉钉配置
			DingTalk: &types.DingTalkConfig{
				WebhookURL: plugin.DingTalkConfig.WebhookURL,
				Secret:     plugin.DingTalkConfig.Secret,
				AtMobiles:  []string{},
				AtAll:      false,
				Title:      title,                  // 钉钉标题
				Text:       dingTalkContent,        // 钉钉内容
				MsgType:    types.DingTalkMarkdown, // 使用Markdown格式
			},
			Enabled:        true,
			RunImmediately: false, // 设置为true可立即执行插件进行测试
		},
	}
}

// 生成标题和内容
func (p *DigitalplatRenewalPlugin) generateTitleAndContent() (title, dingTalkContent, emailContent string) {
	currentTime := time.Now().Format("2006-01-02 15:04:05")
	title = "🌐 Digitalplat域名续期提醒"

	// 钉钉内容（Markdown格式）
	dingTalkContent = fmt.Sprintf(`**提醒时间**: %s

---

### 📋 续期说明
- **域名服务商**: Digitalplat
- **续期周期**: 每180天需要续期一次
- **网址**: [Digitalplat](https://dash.domain.digitalplat.org/)
---
*系统自动发送，请勿回复*`, currentTime)

	// 邮件内容（HTML格式）
	emailContent = fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>%s</title>
	<style>
		body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #fff; margin: 0; padding: 20px; }
		.container { max-width: 600px; margin: 0 auto; }
		.header { background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%); color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
		.content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
		.section { margin-bottom: 20px; }
		.section h3 { color: #495057; margin-bottom: 10px; }
		.info-box { background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; margin-bottom: 15px; }
		.steps { background: white; padding: 15px; border-radius: 5px; }
		.steps ol { margin: 0; padding-left: 20px; }
		.steps li { margin-bottom: 8px; }
		.warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; color: #856404; }
		.footer { text-align: center; color: #6c757d; font-size: 12px; margin-top: 20px; }
	</style>
</head>
<body>
	<div class="container">
		<div class="header">
			<h1>🌐 Digitalplat域名续期提醒</h1>
		</div>
		<div class="content">
			<div class="info-box">
				<strong>提醒时间</strong>: %s
			</div>
			
			<div class="section">
				<h3>📋 续期说明</h3>
				<div class="info-box">
					<p><strong>域名服务商</strong>: Digitalplat</p>
					<p><strong>续期周期</strong>: 每180天需要续期一次</p>
					<p><strong>网址</strong>: <a href="https://dash.domain.digitalplat.org/">Digitalplat</a></p>
				</div>
			</div>
		</div>
		<div class="footer">
			系统自动发送，请勿回复
		</div>
	</div>
</body>
</html>`, title, currentTime)

	return title, dingTalkContent, emailContent
}
