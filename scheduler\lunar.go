package scheduler

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"taskReminder/types"

	"github.com/6tail/lunar-go/calendar"
)

// 农历任务
type LunarTask struct {
	config   *types.TaskConfig
	stopChan chan struct{}
}

// 农历cron表达式结构
type LunarCronExpr struct {
	Minute int // 分钟 (0-59)
	Hour   int // 小时 (0-23)
	Day    int // 农历日 (1-30, * 表示每天)
	Month  int // 农历月 (1-12, * 表示每月)
}

// 添加农历任务
func (s *TaskScheduler) addLunarTask(taskID string, config *types.TaskConfig) error {
	if config.Schedule.LunarCron == "" {
		return ErrLunarCronEmpty
	}

	// 验证农历cron表达式格式
	if _, err := s.parseLunarCron(config.Schedule.LunarCron); err != nil {
		return fmt.Errorf("农历cron表达式格式错误: %v", err)
	}

	lunarTask := &LunarTask{
		config:   config,
		stopChan: make(chan struct{}),
	}

	s.lunarTasks[taskID] = lunarTask

	// 启动农历任务协程
	go s.runLunarTask(taskID, lunarTask)

	s.logger.Printf("添加农历任务成功: %s (%s)", taskID, config.Schedule.LunarCron)
	return nil
}

// 运行农历任务
func (s *TaskScheduler) runLunarTask(taskID string, task *LunarTask) {
	ticker := time.NewTicker(time.Minute) // 每分钟检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if s.shouldExecuteLunarTask(task.config.Schedule.LunarCron) {
				s.executeTask(taskID, task.config)
			}
		case <-task.stopChan:
			return
		}
	}
}

// 解析农历cron表达式
// 格式: "分钟 小时 农历日 农历月"
// 示例: "0 9 1 *" - 每月农历初一上午9点
// 示例: "30 14 15 3" - 农历三月十五下午2点30分
// 示例: "0 0 * *" - 每天农历0点
func (s *TaskScheduler) parseLunarCron(lunarCron string) (*LunarCronExpr, error) {
	parts := strings.Fields(lunarCron)
	if len(parts) != 4 {
		return nil, fmt.Errorf("农历cron表达式必须包含4个部分: 分钟 小时 农历日 农历月")
	}

	expr := &LunarCronExpr{}

	// 解析分钟
	if parts[0] == "*" {
		expr.Minute = -1 // -1 表示任意
	} else {
		minute, err := strconv.Atoi(parts[0])
		if err != nil || minute < 0 || minute > 59 {
			return nil, fmt.Errorf("分钟必须是0-59之间的数字或*")
		}
		expr.Minute = minute
	}

	// 解析小时
	if parts[1] == "*" {
		expr.Hour = -1 // -1 表示任意
	} else {
		hour, err := strconv.Atoi(parts[1])
		if err != nil || hour < 0 || hour > 23 {
			return nil, fmt.Errorf("小时必须是0-23之间的数字或*")
		}
		expr.Hour = hour
	}

	// 解析农历日
	if parts[2] == "*" {
		expr.Day = -1 // -1 表示任意
	} else {
		day, err := strconv.Atoi(parts[2])
		if err != nil || day < 1 || day > 30 {
			return nil, fmt.Errorf("农历日必须是1-30之间的数字或*")
		}
		expr.Day = day
	}

	// 解析农历月
	if parts[3] == "*" {
		expr.Month = -1 // -1 表示任意
	} else {
		month, err := strconv.Atoi(parts[3])
		if err != nil || month < 1 || month > 12 {
			return nil, fmt.Errorf("农历月必须是1-12之间的数字或*")
		}
		expr.Month = month
	}

	return expr, nil
}

// 检查是否应该执行农历任务
func (s *TaskScheduler) shouldExecuteLunarTask(lunarCron string) bool {
	expr, err := s.parseLunarCron(lunarCron)
	if err != nil {
		s.logger.Printf("解析农历cron表达式失败: %v", err)
		return false
	}

	now := time.Now()
	lunar := calendar.NewSolarFromYmd(now.Year(), int(now.Month()), now.Day()).GetLunar()

	// 检查分钟
	if expr.Minute != -1 && now.Minute() != expr.Minute {
		return false
	}

	// 检查小时
	if expr.Hour != -1 && now.Hour() != expr.Hour {
		return false
	}

	// 检查农历日
	if expr.Day != -1 && lunar.GetDay() != expr.Day {
		return false
	}

	// 检查农历月
	if expr.Month != -1 && lunar.GetMonth() != expr.Month {
		return false
	}

	return true
}
